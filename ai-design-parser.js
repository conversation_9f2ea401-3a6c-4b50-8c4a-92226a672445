// AI Design Data Parser for Embroidery Thread Art

class AIDesignParser {
    constructor(canvas, ctx, patternGenerator) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.patternGenerator = patternGenerator;
    }

    renderDesignFromAI(designData, usedColors) {
        try {
            // Clear canvas
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            
            const design = designData.design;
            
            // Render in order: background -> main patterns -> focus points -> details -> borders
            this.renderBackgroundElements(design.backgroundElements);
            this.renderMainPatterns(design.mainPatterns, usedColors);
            this.renderFocusPoints(design.focusPoints, usedColors);
            this.renderDetailElements(design.detailElements, usedColors);
            this.renderBorders(design.borders, usedColors);
            
            return {
                success: true,
                metadata: designData.metadata,
                colorsUsed: this.extractColorsFromDesign(design)
            };
        } catch (error) {
            console.error('Error rendering AI design:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    renderBackgroundElements(backgroundElements) {
        if (!backgroundElements || backgroundElements.length === 0) return;

        backgroundElements.forEach(element => {
            const originalAlpha = this.ctx.globalAlpha;
            this.ctx.globalAlpha = element.opacity || 0.3;

            switch (element.type) {
                case 'gradient':
                    this.renderGradientBackground(element);
                    break;
                case 'texture':
                    this.renderTextureBackground(element);
                    break;
                case 'pattern':
                    this.renderPatternBackground(element);
                    break;
                default:
                    this.renderSolidBackground(element);
            }

            this.ctx.globalAlpha = originalAlpha;
        });
    }

    renderGradientBackground(element) {
        const coords = element.coordinates;
        const gradient = this.ctx.createLinearGradient(
            coords.x1 || 0, 
            coords.y1 || 0, 
            coords.x2 || this.canvas.width, 
            coords.y2 || this.canvas.height
        );

        element.colors.forEach((color, index) => {
            gradient.addColorStop(index / (element.colors.length - 1), color);
        });

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    renderTextureBackground(element) {
        const color = element.colors[0];
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 1;

        // Create subtle texture lines
        for (let i = 0; i < 30; i++) {
            this.ctx.beginPath();
            const startX = Math.random() * this.canvas.width;
            const startY = Math.random() * this.canvas.height;
            const endX = startX + (Math.random() - 0.5) * 100;
            const endY = startY + (Math.random() - 0.5) * 100;
            
            this.ctx.moveTo(startX, startY);
            this.ctx.lineTo(endX, endY);
            this.ctx.stroke();
        }
    }

    renderPatternBackground(element) {
        const color = element.colors[0];
        const spacing = 40;
        
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 0.5;
        
        // Create grid pattern
        for (let x = spacing; x < this.canvas.width; x += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = spacing; y < this.canvas.height; y += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    renderSolidBackground(element) {
        this.ctx.fillStyle = element.colors[0];
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    renderMainPatterns(mainPatterns, usedColors) {
        if (!mainPatterns || mainPatterns.length === 0) return;

        mainPatterns.forEach(pattern => {
            const coords = pattern.coordinates;
            const params = pattern.parameters || {};
            const lineWidth = pattern.lineWidth || 3;
            const size = this.getSizeMultiplier(pattern.size);

            usedColors.add(pattern.color);

            switch (pattern.type) {
                case 'spiral':
                    this.patternGenerator.drawSpiral(
                        coords.centerX, coords.centerY,
                        (params.radius || 80) * size,
                        params.turns || 3,
                        pattern.color,
                        lineWidth
                    );
                    break;

                case 'wave':
                    const waveLength = (params.length || 200) * size;
                    this.patternGenerator.drawWave(
                        coords.centerX - waveLength/2, coords.centerY,
                        coords.centerX + waveLength/2, coords.centerY,
                        (params.amplitude || 30) * size,
                        params.frequency || 2,
                        pattern.color,
                        lineWidth
                    );
                    break;

                case 'mandala':
                    this.patternGenerator.drawMandala(
                        coords.centerX, coords.centerY,
                        (params.radius || 80) * size,
                        params.petals || 8,
                        pattern.color,
                        lineWidth
                    );
                    break;

                case 'geometric':
                    this.patternGenerator.drawGeometric(
                        coords.centerX, coords.centerY,
                        (params.size || 60) * size,
                        params.sides || 6,
                        params.layers || 3,
                        pattern.color,
                        lineWidth
                    );
                    break;

                case 'flower':
                    this.patternGenerator.drawFlower(
                        coords.centerX, coords.centerY,
                        (params.petalLength || 60) * size,
                        params.petals || 6,
                        pattern.color,
                        lineWidth
                    );
                    break;

                case 'circle':
                    this.ctx.strokeStyle = pattern.color;
                    this.ctx.lineWidth = lineWidth;
                    this.ctx.beginPath();
                    this.ctx.arc(
                        coords.centerX, coords.centerY,
                        (params.radius || 50) * size,
                        0, 2 * Math.PI
                    );
                    this.ctx.stroke();
                    break;

                case 'line':
                    this.renderLinePattern(coords, params, pattern.color, lineWidth, size);
                    break;

                case 'curve':
                    this.renderCurvePattern(coords, params, pattern.color, lineWidth, size);
                    break;

                default:
                    console.warn(`Unknown pattern type: ${pattern.type}`);
            }
        });
    }

    renderLinePattern(coords, params, color, lineWidth, size) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        
        const numLines = params.count || 8;
        const length = (params.length || 60) * size;
        
        for (let i = 0; i < numLines; i++) {
            const angle = (i * 2 * Math.PI) / numLines;
            const endX = coords.centerX + length * Math.cos(angle);
            const endY = coords.centerY + length * Math.sin(angle);
            
            this.ctx.beginPath();
            this.ctx.moveTo(coords.centerX, coords.centerY);
            this.ctx.lineTo(endX, endY);
            this.ctx.stroke();
        }
    }

    renderCurvePattern(coords, params, color, lineWidth, size) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        
        const numCurves = params.count || 4;
        const length = (params.length || 80) * size;
        
        for (let i = 0; i < numCurves; i++) {
            const angle = (i * 2 * Math.PI) / numCurves;
            const startX = coords.centerX + 20 * Math.cos(angle);
            const startY = coords.centerY + 20 * Math.sin(angle);
            const endX = coords.centerX + length * Math.cos(angle);
            const endY = coords.centerY + length * Math.sin(angle);
            
            this.ctx.beginPath();
            this.ctx.moveTo(startX, startY);
            
            const controlX = (startX + endX) / 2 + 30 * Math.cos(angle + Math.PI/2);
            const controlY = (startY + endY) / 2 + 30 * Math.sin(angle + Math.PI/2);
            
            this.ctx.quadraticCurveTo(controlX, controlY, endX, endY);
            this.ctx.stroke();
        }
    }

    renderFocusPoints(focusPoints, usedColors) {
        if (!focusPoints || focusPoints.length === 0) return;

        focusPoints.forEach(point => {
            const coords = point.coordinates;
            const radius = point.radius || 60;
            const intensity = point.intensity || 1.0;
            
            usedColors.add(point.color);

            // Adjust line width based on intensity
            const lineWidth = Math.max(1, Math.round(3 * intensity));

            switch (point.type) {
                case 'spiral':
                    this.patternGenerator.drawSpiral(
                        coords.x, coords.y,
                        radius, 2.5,
                        point.color, lineWidth
                    );
                    break;

                case 'mandala':
                    this.patternGenerator.drawMandala(
                        coords.x, coords.y,
                        radius, 8,
                        point.color, lineWidth
                    );
                    break;

                case 'flower':
                    this.patternGenerator.drawFlower(
                        coords.x, coords.y,
                        radius, 8,
                        point.color, lineWidth
                    );
                    break;

                case 'geometric':
                    this.patternGenerator.drawGeometric(
                        coords.x, coords.y,
                        radius, 6, 3,
                        point.color, lineWidth
                    );
                    break;

                default:
                    // Default to concentric circles
                    this.ctx.strokeStyle = point.color;
                    this.ctx.lineWidth = lineWidth;
                    
                    for (let i = 1; i <= 3; i++) {
                        this.ctx.beginPath();
                        this.ctx.arc(coords.x, coords.y, radius * i / 3, 0, 2 * Math.PI);
                        this.ctx.stroke();
                    }
            }
        });
    }

    renderDetailElements(detailElements, usedColors) {
        if (!detailElements || detailElements.length === 0) return;

        detailElements.forEach(element => {
            const area = element.area;
            const density = element.density || 0.5;
            
            usedColors.add(element.color);

            switch (element.type) {
                case 'dots':
                    this.renderDots(area, element.color, density);
                    break;
                case 'lines':
                    this.renderDetailLines(area, element.color, density);
                    break;
                case 'texture':
                    this.renderDetailTexture(area, element.color, density);
                    break;
            }
        });
    }

    renderDots(area, color, density) {
        this.ctx.fillStyle = color;
        const numDots = Math.floor(density * 50);
        
        for (let i = 0; i < numDots; i++) {
            const x = area.x + Math.random() * area.width;
            const y = area.y + Math.random() * area.height;
            const radius = 1 + Math.random() * 2;
            
            this.ctx.beginPath();
            this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }

    renderDetailLines(area, color, density) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 1;
        const numLines = Math.floor(density * 20);
        
        for (let i = 0; i < numLines; i++) {
            const startX = area.x + Math.random() * area.width;
            const startY = area.y + Math.random() * area.height;
            const endX = startX + (Math.random() - 0.5) * 40;
            const endY = startY + (Math.random() - 0.5) * 40;
            
            this.ctx.beginPath();
            this.ctx.moveTo(startX, startY);
            this.ctx.lineTo(endX, endY);
            this.ctx.stroke();
        }
    }

    renderDetailTexture(area, color, density) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 0.5;
        const spacing = 10 / density;
        
        for (let x = area.x; x <= area.x + area.width; x += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, area.y);
            this.ctx.lineTo(x, area.y + area.height);
            this.ctx.stroke();
        }
    }

    renderBorders(borders, usedColors) {
        if (!borders || borders.length === 0) return;

        borders.forEach(border => {
            const margin = border.margin || 20;
            const width = border.width || 3;
            
            usedColors.add(border.color);
            
            this.ctx.strokeStyle = border.color;
            this.ctx.lineWidth = width;

            switch (border.type) {
                case 'simple':
                    this.ctx.beginPath();
                    this.ctx.rect(margin, margin, 
                        this.canvas.width - 2*margin, 
                        this.canvas.height - 2*margin);
                    this.ctx.stroke();
                    break;

                case 'decorative':
                    this.renderDecorativeBorder(margin, border.color, width);
                    break;

                case 'flowing':
                    this.renderFlowingBorder(margin, border.color, width);
                    break;

                default:
                    // Default to simple border
                    this.ctx.beginPath();
                    this.ctx.rect(margin, margin, 
                        this.canvas.width - 2*margin, 
                        this.canvas.height - 2*margin);
                    this.ctx.stroke();
            }
        });
    }

    renderDecorativeBorder(margin, color, width) {
        const spacing = 30;
        
        // Top and bottom decorative elements
        for (let x = margin + spacing; x < this.canvas.width - margin; x += spacing) {
            this.renderDecorativeElement(x, margin, color);
            this.renderDecorativeElement(x, this.canvas.height - margin, color);
        }
        
        // Left and right decorative elements
        for (let y = margin + spacing; y < this.canvas.height - margin; y += spacing) {
            this.renderDecorativeElement(margin, y, color);
            this.renderDecorativeElement(this.canvas.width - margin, y, color);
        }
    }

    renderDecorativeElement(x, y, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 1;
        
        this.ctx.beginPath();
        this.ctx.moveTo(x, y - 5);
        this.ctx.lineTo(x + 5, y);
        this.ctx.lineTo(x, y + 5);
        this.ctx.lineTo(x - 5, y);
        this.ctx.closePath();
        this.ctx.stroke();
    }

    renderFlowingBorder(margin, color, width) {
        // Create flowing wave borders
        this.patternGenerator.drawWave(
            margin, margin,
            this.canvas.width - margin, margin,
            10, 5, color, width
        );
        
        this.patternGenerator.drawWave(
            margin, this.canvas.height - margin,
            this.canvas.width - margin, this.canvas.height - margin,
            10, 5, color, width
        );
    }

    getSizeMultiplier(size) {
        switch (size) {
            case 'small': return 0.7;
            case 'medium': return 1.0;
            case 'large': return 1.4;
            default: return 1.0;
        }
    }

    extractColorsFromDesign(design) {
        const colors = new Set();
        
        // Extract from main patterns
        design.mainPatterns.forEach(pattern => {
            if (pattern.color) colors.add(pattern.color);
        });
        
        // Extract from focus points
        design.focusPoints.forEach(point => {
            if (point.color) colors.add(point.color);
        });
        
        // Extract from detail elements
        design.detailElements.forEach(element => {
            if (element.color) colors.add(element.color);
        });
        
        // Extract from borders
        design.borders.forEach(border => {
            if (border.color) colors.add(border.color);
        });
        
        // Extract from background elements
        design.backgroundElements.forEach(bg => {
            if (bg.colors) {
                bg.colors.forEach(color => colors.add(color));
            }
        });
        
        return Array.from(colors);
    }
}
