# 🧵 Embroidery Thread Art Designer

A local web application for designing and visualizing embroidery thread art pieces. This tool helps you plan thread art creations that will be made by gluing colored embroidery threads onto canvas and finishing with an epoxy coating.

## Features

### 🎨 Drawing Tools
- **Line Tool**: Draw straight lines and continuous freehand lines
- **Curve Tool**: Create smooth curved lines
- **Circle Tool**: Draw perfect circles
- **Rectangle Tool**: Create rectangular shapes

### 🌈 Color Management
- **Thread Color Palette**: Pre-loaded with authentic embroidery thread colors (DMC brand references)
- **Custom Colors**: Use the color picker for custom thread colors
- **Color Categories**: Reds, blues, greens, yellows, purples, neutrals, browns, pinks, and metallics
- **Used Colors Tracking**: Automatically tracks colors used in your design

### 📐 Design Features
- **Adjustable Thread Width**: Simulate different thread thicknesses (1-10px)
- **Grid Overlay**: Optional grid for precise placement
- **Frame Visualization**: Toggle wooden frame border effect
- **Epoxy Effect**: Simulate the glossy finish of epoxy coating

### 🔄 Pattern Generator
Pre-built patterns to inspire your designs:
- **Spiral**: Classic spiral patterns with customizable turns
- **Wave**: Flowing wave patterns
- **Mandala**: Symmetrical mandala designs
- **Geometric**: Layered geometric shapes

### 💾 Project Management
- **Save Design**: Save your work to browser local storage
- **Load Design**: Restore previously saved designs
- **Export Image**: Download your design as a PNG file
- **Undo Function**: Step back through your drawing history
- **Clear Canvas**: Start fresh with a clean canvas

### 📊 Planning Tools
- **Material List**: Automatically generates list of required materials
- **Thread Length Estimation**: Rough estimate of thread needed
- **Color Count**: Track how many different colors you're using
- **Canvas Dimensions**: Display current canvas size

## How to Use

### Getting Started
1. Open `index.html` in your web browser
2. The application will load with a blank canvas ready for drawing

### Basic Drawing
1. **Select a Tool**: Click on Line, Curve, Circle, or Rectangle in the toolbar
2. **Choose a Color**: Click on any color in the palette or use the custom color picker
3. **Adjust Thread Width**: Use the slider to set thread thickness
4. **Draw on Canvas**: Click and drag on the canvas to create your design

### Using Patterns
1. Select a thread color
2. Click on any pattern button (Spiral, Wave, Mandala, Geometric)
3. The pattern will be drawn at the center of the canvas
4. Combine multiple patterns and colors for complex designs

### Canvas Settings
- **Show Grid**: Toggle grid overlay for precise alignment
- **Show Frame**: Toggle the wooden frame border
- **Epoxy Effect**: Add a glossy overlay to simulate the final finish

### Saving and Exporting
- **Save**: Stores your design in browser memory (persists between sessions)
- **Load**: Restores your last saved design
- **Export**: Downloads your design as a PNG image file

## Design Tips

### Color Combinations
The app includes several pre-defined color schemes:
- **Sunset**: Warm oranges, yellows, pinks, and reds
- **Ocean**: Cool blues and turquoise
- **Forest**: Natural greens and browns
- **Royal**: Purple, gold, black, and white
- **Spring**: Fresh greens, yellows, pinks, and blues
- **Autumn**: Warm oranges, browns, golds, and reds

### Planning Your Physical Project
1. **Start Simple**: Begin with basic shapes and gradually add complexity
2. **Consider Thread Direction**: Plan how threads will lay on the canvas
3. **Color Balance**: Use the color counter to ensure good color distribution
4. **Frame Consideration**: Keep important elements away from edges
5. **Epoxy Compatibility**: Remember that epoxy will add a glossy finish

### Thread Art Techniques
- **Layering**: Use different thread widths to create depth
- **Texture**: Vary line density for different textures
- **Flow**: Follow natural curves and patterns
- **Contrast**: Use complementary colors for visual impact

## Technical Details

### Browser Compatibility
- Works in all modern browsers (Chrome, Firefox, Safari, Edge)
- Uses HTML5 Canvas for drawing
- No external dependencies required
- Responsive design works on desktop and tablet

### File Structure
```
├── index.html          # Main application page
├── styles.css          # Application styling
├── app.js             # Main application logic
├── thread-colors.js   # Color palette definitions
├── patterns.js        # Pattern generation functions
└── README.md          # This documentation
```

### Local Storage
- Designs are saved to browser's local storage
- Data persists between browser sessions
- No server required - everything runs locally

## Customization

### Adding New Colors
Edit `thread-colors.js` to add new thread colors:
```javascript
{ name: 'Your Color', hex: '#HEXCODE', brand: 'Brand Reference' }
```

### Creating New Patterns
Add new pattern functions to `patterns.js` and update the UI in `index.html`.

### Modifying Canvas Size
Change the canvas dimensions in `index.html`:
```html
<canvas id="main-canvas" width="800" height="600"></canvas>
```

## Troubleshooting

### Common Issues
- **Drawings not appearing**: Check that a color is selected
- **Can't save design**: Ensure browser allows local storage
- **Patterns not working**: Try refreshing the page
- **Export not working**: Check browser's download settings

### Browser Support
- Requires HTML5 Canvas support
- JavaScript must be enabled
- Local storage must be available

## Future Enhancements

Potential features for future versions:
- Multiple canvas layers
- Advanced pattern editor
- Thread cost calculator
- 3D preview mode
- Collaborative design sharing
- Mobile touch support optimization

---

**Happy Designing!** 🎨

Create beautiful embroidery thread art designs and bring them to life with real materials.
