// Environment Configuration Loader for Embroidery Thread Art Designer

class EnvironmentConfig {
    constructor() {
        this.config = {};
        this.envLoaded = false;
        this.loadEnvironmentConfig();
    }

    async loadEnvironmentConfig() {
        try {
            // Try to load .env file if it exists
            const envResponse = await fetch('.env');
            if (envResponse.ok) {
                const envText = await envResponse.text();
                this.parseEnvFile(envText);
                this.envLoaded = true;
                console.log('Environment configuration loaded from .env file');
            }
        } catch (error) {
            console.log('No .env file found, using default configuration');
        }

        // Set default values
        this.setDefaults();
    }

    parseEnvFile(envText) {
        const lines = envText.split('\n');
        
        lines.forEach(line => {
            // Skip empty lines and comments
            line = line.trim();
            if (!line || line.startsWith('#')) return;
            
            // Parse key=value pairs
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim();
                
                // Remove quotes if present
                const cleanValue = value.replace(/^["']|["']$/g, '');
                this.config[key] = cleanValue;
            }
        });
    }

    setDefaults() {
        // Set default values if not provided
        this.config.APP_NAME = this.config.APP_NAME || 'Embroidery Thread Art Designer';
        this.config.APP_VERSION = this.config.APP_VERSION || '1.0.0';
        this.config.CANVAS_WIDTH = parseInt(this.config.CANVAS_WIDTH) || 800;
        this.config.CANVAS_HEIGHT = parseInt(this.config.CANVAS_HEIGHT) || 600;
        this.config.AI_TEMPERATURE = parseFloat(this.config.AI_TEMPERATURE) || 0.7;
        this.config.AI_MAX_TOKENS = parseInt(this.config.AI_MAX_TOKENS) || 2048;
        this.config.DEBUG_MODE = this.config.DEBUG_MODE === 'true';
        this.config.LOG_LEVEL = this.config.LOG_LEVEL || 'info';
    }

    get(key) {
        return this.config[key];
    }

    set(key, value) {
        this.config[key] = value;
    }

    has(key) {
        return key in this.config;
    }

    getGeminiAPIKey() {
        // Priority: Environment variable > localStorage
        if (this.config.GEMINI_API_KEY) {
            return this.config.GEMINI_API_KEY;
        }
        
        // Fallback to localStorage
        return localStorage.getItem('gemini-api-key') || '';
    }

    saveGeminiAPIKey(apiKey) {
        // Only save to localStorage if not using environment variable
        if (!this.config.GEMINI_API_KEY) {
            localStorage.setItem('gemini-api-key', apiKey);
        }
    }

    isUsingEnvironmentAPIKey() {
        return !!this.config.GEMINI_API_KEY;
    }

    getCanvasConfig() {
        return {
            width: this.config.CANVAS_WIDTH,
            height: this.config.CANVAS_HEIGHT
        };
    }

    getAIConfig() {
        return {
            temperature: this.config.AI_TEMPERATURE,
            maxTokens: this.config.AI_MAX_TOKENS
        };
    }

    getDebugConfig() {
        return {
            debugMode: this.config.DEBUG_MODE,
            logLevel: this.config.LOG_LEVEL
        };
    }

    getAllConfig() {
        return { ...this.config };
    }

    // Utility method to log configuration (without sensitive data)
    logConfig() {
        if (this.config.DEBUG_MODE) {
            const safeConfig = { ...this.config };
            
            // Mask sensitive information
            if (safeConfig.GEMINI_API_KEY) {
                safeConfig.GEMINI_API_KEY = '***masked***';
            }
            
            console.log('Environment Configuration:', safeConfig);
        }
    }

    // Method to validate required configuration
    validateConfig() {
        const errors = [];
        
        // Check for required API key
        if (!this.getGeminiAPIKey()) {
            errors.push('Gemini API key is required. Set GEMINI_API_KEY in .env file or configure in the UI.');
        }
        
        // Validate canvas dimensions
        if (this.config.CANVAS_WIDTH < 100 || this.config.CANVAS_WIDTH > 2000) {
            errors.push('Canvas width must be between 100 and 2000 pixels.');
        }
        
        if (this.config.CANVAS_HEIGHT < 100 || this.config.CANVAS_HEIGHT > 2000) {
            errors.push('Canvas height must be between 100 and 2000 pixels.');
        }
        
        // Validate AI configuration
        if (this.config.AI_TEMPERATURE < 0 || this.config.AI_TEMPERATURE > 2) {
            errors.push('AI temperature must be between 0 and 2.');
        }
        
        if (this.config.AI_MAX_TOKENS < 100 || this.config.AI_MAX_TOKENS > 8192) {
            errors.push('AI max tokens must be between 100 and 8192.');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Method to create a configuration status report
    getConfigStatus() {
        const validation = this.validateConfig();
        
        return {
            envFileLoaded: this.envLoaded,
            usingEnvironmentAPIKey: this.isUsingEnvironmentAPIKey(),
            hasAPIKey: !!this.getGeminiAPIKey(),
            canvasConfig: this.getCanvasConfig(),
            aiConfig: this.getAIConfig(),
            debugConfig: this.getDebugConfig(),
            validation: validation
        };
    }
}

// Create global instance
const envConfig = new EnvironmentConfig();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnvironmentConfig;
}
