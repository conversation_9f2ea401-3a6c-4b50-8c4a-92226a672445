<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Embroidery Thread Art Designer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <h1>🧵 Embroidery Thread Art Designer</h1>
            <p>Design and visualize your thread art creations</p>
        </header>

        <div class="main-content">
            <!-- Toolbar -->
            <div class="toolbar">
                <div class="tool-section">
                    <h3>Drawing Tools</h3>
                    <button id="line-tool" class="tool-btn active" data-tool="line">
                        📏 Line
                    </button>
                    <button id="curve-tool" class="tool-btn" data-tool="curve">
                        〰️ Curve
                    </button>
                    <button id="circle-tool" class="tool-btn" data-tool="circle">
                        ⭕ Circle
                    </button>
                    <button id="rectangle-tool" class="tool-btn" data-tool="rectangle">
                        ⬜ Rectangle
                    </button>
                </div>

                <div class="tool-section">
                    <h3>Thread Colors</h3>
                    <div id="color-palette" class="color-palette">
                        <!-- Colors will be populated by JavaScript -->
                    </div>
                    <input type="color" id="custom-color" value="#ff0000">
                    <label for="custom-color">Custom</label>
                </div>

                <div class="tool-section">
                    <h3>Thread Width</h3>
                    <input type="range" id="thread-width" min="1" max="10" value="3">
                    <span id="width-display">3px</span>
                </div>

                <div class="tool-section">
                    <h3>Canvas Settings</h3>
                    <label>
                        <input type="checkbox" id="show-grid"> Show Grid
                    </label>
                    <label>
                        <input type="checkbox" id="show-frame" checked> Show Frame
                    </label>
                    <label>
                        <input type="checkbox" id="epoxy-effect"> Epoxy Effect
                    </label>
                </div>

                <div class="tool-section">
                    <h3>Actions</h3>
                    <button id="clear-canvas" class="action-btn">🗑️ Clear</button>
                    <button id="undo" class="action-btn">↶ Undo</button>
                    <button id="save-design" class="action-btn">💾 Save</button>
                    <button id="load-design" class="action-btn">📁 Load</button>
                    <button id="export-image" class="action-btn">📸 Export</button>
                </div>

                <div class="tool-section">
                    <h3>Patterns</h3>
                    <button id="spiral-pattern" class="pattern-btn">🌀 Spiral</button>
                    <button id="wave-pattern" class="pattern-btn">〰️ Wave</button>
                    <button id="mandala-pattern" class="pattern-btn">🔸 Mandala</button>
                    <button id="geometric-pattern" class="pattern-btn">🔷 Geometric</button>
                </div>

                <div class="tool-section">
                    <h3>Design Inspiration</h3>
                    <button id="random-idea" class="inspiration-btn">✨ Random Idea</button>
                    <button id="pattern-combo" class="inspiration-btn">🎲 Pattern Mix</button>
                    <button id="show-themes" class="inspiration-btn">🎨 Browse Themes</button>
                </div>

                <div class="tool-section">
                    <h3>AI Settings</h3>

                    <label for="gemini-api-key">Gemini API Key:</label>
                    <div class="api-key-container">
                        <input type="password" id="gemini-api-key" placeholder="Enter your Gemini API key">
                        <button id="save-api-key" class="save-key-btn">💾</button>
                    </div>
                    <small class="api-help">Get your free API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></small>

                    <label for="ai-style">Style:</label>
                    <select id="ai-style">
                        <option value="organic">Organic Flow</option>
                        <option value="geometric">Geometric Precision</option>
                        <option value="minimalist">Minimalist Zen</option>
                        <option value="bohemian">Bohemian Tapestry</option>
                        <option value="artDeco">Art Deco Elegance</option>
                    </select>

                    <label for="ai-complexity">Complexity:</label>
                    <select id="ai-complexity">
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                        <option value="very_high">Very High</option>
                    </select>

                    <label for="ai-theme">Theme:</label>
                    <select id="ai-theme">
                        <option value="nature">Nature</option>
                        <option value="abstract" selected>Abstract</option>
                        <option value="seasonal">Seasonal</option>
                        <option value="artistic">Artistic</option>
                    </select>

                    <label for="ai-prompt">Custom Prompt:</label>
                    <textarea id="ai-prompt" rows="4" placeholder="Describe your desired embroidery thread art design...">Create a beautiful embroidery thread art design with flowing patterns and harmonious colors.</textarea>

                    <button id="generate-ai-design" class="ai-generate-btn">🤖 Generate AI Design</button>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-container">
                <canvas id="main-canvas" width="800" height="600"></canvas>
                <div id="canvas-overlay" class="canvas-overlay"></div>
            </div>

            <!-- Info Panel -->
            <div class="info-panel">
                <h3>Design Information</h3>
                <div id="design-stats">
                    <p>Canvas Size: <span id="canvas-size">800 x 600px</span></p>
                    <p>Thread Length: <span id="thread-length">0 inches</span></p>
                    <p>Colors Used: <span id="colors-count">0</span></p>
                </div>

                <!-- Inspiration Section -->
                <div id="inspiration-section" class="inspiration-section" style="display: none;">
                    <h4>💡 Design Inspiration</h4>
                    <div id="current-inspiration" class="current-inspiration">
                        <!-- Inspiration content will be populated by JavaScript -->
                    </div>
                    <div class="inspiration-actions">
                        <button id="apply-inspiration" class="apply-btn">Apply Colors</button>
                        <button id="new-inspiration" class="apply-btn">New Idea</button>
                    </div>
                </div>

                <!-- Theme Browser -->
                <div id="theme-browser" class="theme-browser" style="display: none;">
                    <h4>🎨 Browse by Theme</h4>
                    <div id="theme-tabs" class="theme-tabs">
                        <!-- Theme tabs will be populated by JavaScript -->
                    </div>
                    <div id="theme-ideas" class="theme-ideas">
                        <!-- Theme ideas will be populated by JavaScript -->
                    </div>
                </div>

                <h4>Material List</h4>
                <ul id="material-list">
                    <li>Canvas with frame (800x600px equivalent)</li>
                    <li>Epoxy coating</li>
                </ul>

                <h4>Instructions</h4>
                <ol id="instructions">
                    <li>Select a drawing tool from the toolbar</li>
                    <li>Choose thread colors from the palette</li>
                    <li>Draw your design on the canvas</li>
                    <li>Use patterns for inspiration</li>
                    <li>Export your design when ready</li>
                </ol>
            </div>
        </div>
    </div>

    <script src="thread-colors.js"></script>
    <script src="patterns.js"></script>
    <script src="design-ideas.js"></script>
    <script src="ai-design-engine.js"></script>
    <script src="ai-api-integration.js"></script>
    <script src="ai-design-parser.js"></script>
    <script src="app.js"></script>
</body>
</html>
