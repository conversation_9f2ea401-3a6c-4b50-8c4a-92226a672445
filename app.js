// Main application class
class EmbroideryDesigner {
    constructor() {
        this.canvas = document.getElementById('main-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.patternGenerator = new PatternGenerator(this.canvas, this.ctx);
        
        // Application state
        this.currentTool = 'line';
        this.currentColor = '#DC143C';
        this.threadWidth = 3;
        this.isDrawing = false;
        this.showGrid = false;
        this.showFrame = true;
        this.epoxyEffect = false;
        
        // Drawing state
        this.startX = 0;
        this.startY = 0;
        this.paths = [];
        this.undoStack = [];
        this.usedColors = new Set();
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.populateColorPalette();
        this.drawFrame();
        this.updateStats();
    }

    setupEventListeners() {
        // Canvas events
        this.canvas.addEventListener('mousedown', this.startDrawing.bind(this));
        this.canvas.addEventListener('mousemove', this.draw.bind(this));
        this.canvas.addEventListener('mouseup', this.stopDrawing.bind(this));
        this.canvas.addEventListener('mouseout', this.stopDrawing.bind(this));

        // Tool selection
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectTool(e.target.dataset.tool);
            });
        });

        // Color selection
        document.getElementById('custom-color').addEventListener('change', (e) => {
            this.currentColor = e.target.value;
        });

        // Thread width
        const widthSlider = document.getElementById('thread-width');
        widthSlider.addEventListener('input', (e) => {
            this.threadWidth = parseInt(e.target.value);
            document.getElementById('width-display').textContent = `${this.threadWidth}px`;
        });

        // Canvas settings
        document.getElementById('show-grid').addEventListener('change', (e) => {
            this.showGrid = e.target.checked;
            this.redraw();
        });

        document.getElementById('show-frame').addEventListener('change', (e) => {
            this.showFrame = e.target.checked;
            this.redraw();
        });

        document.getElementById('epoxy-effect').addEventListener('change', (e) => {
            this.epoxyEffect = e.target.checked;
            this.toggleEpoxyEffect();
        });

        // Action buttons
        document.getElementById('clear-canvas').addEventListener('click', () => {
            this.clearCanvas();
        });

        document.getElementById('undo').addEventListener('click', () => {
            this.undo();
        });

        document.getElementById('save-design').addEventListener('click', () => {
            this.saveDesign();
        });

        document.getElementById('load-design').addEventListener('click', () => {
            this.loadDesign();
        });

        document.getElementById('export-image').addEventListener('click', () => {
            this.exportImage();
        });

        // Pattern buttons
        document.getElementById('spiral-pattern').addEventListener('click', () => {
            this.drawPattern('spiral');
        });

        document.getElementById('wave-pattern').addEventListener('click', () => {
            this.drawPattern('wave');
        });

        document.getElementById('mandala-pattern').addEventListener('click', () => {
            this.drawPattern('mandala');
        });

        document.getElementById('geometric-pattern').addEventListener('click', () => {
            this.drawPattern('geometric');
        });
    }

    populateColorPalette() {
        const palette = document.getElementById('color-palette');
        const colors = getAllColors();
        
        colors.forEach((color, index) => {
            const swatch = document.createElement('div');
            swatch.className = 'color-swatch';
            swatch.style.backgroundColor = color.hex;
            swatch.title = `${color.name} (${color.brand})`;
            swatch.addEventListener('click', () => {
                this.selectColor(color.hex);
                document.querySelectorAll('.color-swatch').forEach(s => s.classList.remove('active'));
                swatch.classList.add('active');
            });
            
            if (index === 0) {
                swatch.classList.add('active');
            }
            
            palette.appendChild(swatch);
        });
    }

    selectTool(tool) {
        this.currentTool = tool;
        document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-tool="${tool}"]`).classList.add('active');
    }

    selectColor(color) {
        this.currentColor = color;
        this.usedColors.add(color);
        this.updateStats();
    }

    startDrawing(e) {
        this.isDrawing = true;
        const rect = this.canvas.getBoundingClientRect();
        this.startX = e.clientX - rect.left;
        this.startY = e.clientY - rect.top;
        this.lastX = null;
        this.lastY = null;

        this.saveState();
    }

    draw(e) {
        if (!this.isDrawing) return;

        const rect = this.canvas.getBoundingClientRect();
        const currentX = e.clientX - rect.left;
        const currentY = e.clientY - rect.top;

        this.ctx.strokeStyle = this.currentColor;
        this.ctx.lineWidth = this.threadWidth;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';

        switch (this.currentTool) {
            case 'line':
                // For continuous line drawing
                if (!this.lastX || !this.lastY) {
                    this.lastX = this.startX;
                    this.lastY = this.startY;
                }
                this.drawLine(this.lastX, this.lastY, currentX, currentY);
                this.lastX = currentX;
                this.lastY = currentY;
                break;
            case 'curve':
                this.drawCurve(this.startX, this.startY, currentX, currentY);
                break;
        }
    }

    stopDrawing(e) {
        if (!this.isDrawing) return;
        
        this.isDrawing = false;
        const rect = this.canvas.getBoundingClientRect();
        const endX = e.clientX - rect.left;
        const endY = e.clientY - rect.top;
        
        switch (this.currentTool) {
            case 'circle':
                this.drawCircle(this.startX, this.startY, endX, endY);
                break;
            case 'rectangle':
                this.drawRectangle(this.startX, this.startY, endX, endY);
                break;
        }
        
        this.updateStats();
    }

    drawLine(x1, y1, x2, y2) {
        this.ctx.beginPath();
        this.ctx.moveTo(x1, y1);
        this.ctx.lineTo(x2, y2);
        this.ctx.stroke();
    }

    drawCurve(x1, y1, x2, y2) {
        this.ctx.beginPath();
        this.ctx.moveTo(x1, y1);
        const controlX = (x1 + x2) / 2;
        const controlY = y1 - Math.abs(x2 - x1) / 4;
        this.ctx.quadraticCurveTo(controlX, controlY, x2, y2);
        this.ctx.stroke();
    }

    drawCircle(centerX, centerY, edgeX, edgeY) {
        const radius = Math.sqrt((edgeX - centerX) ** 2 + (edgeY - centerY) ** 2);
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        this.ctx.stroke();
    }

    drawRectangle(x1, y1, x2, y2) {
        this.ctx.beginPath();
        this.ctx.rect(x1, y1, x2 - x1, y2 - y1);
        this.ctx.stroke();
    }

    drawPattern(patternType) {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        switch (patternType) {
            case 'spiral':
                this.patternGenerator.drawSpiral(centerX, centerY, 100, 3, this.currentColor, this.threadWidth);
                break;
            case 'wave':
                this.patternGenerator.drawWave(50, centerY, this.canvas.width - 50, centerY, 30, 2, this.currentColor, this.threadWidth);
                break;
            case 'mandala':
                this.patternGenerator.drawMandala(centerX, centerY, 120, 8, this.currentColor, this.threadWidth);
                break;
            case 'geometric':
                this.patternGenerator.drawGeometric(centerX, centerY, 100, 6, 3, this.currentColor, this.threadWidth);
                break;
        }
        
        this.usedColors.add(this.currentColor);
        this.updateStats();
    }

    drawFrame() {
        if (!this.showFrame) return;
        
        this.ctx.strokeStyle = '#8B4513';
        this.ctx.lineWidth = 6;
        this.ctx.strokeRect(3, 3, this.canvas.width - 6, this.canvas.height - 6);
    }

    drawGrid() {
        if (!this.showGrid) return;
        
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.lineWidth = 1;
        
        const spacing = 20;
        
        for (let x = 0; x <= this.canvas.width; x += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = 0; y <= this.canvas.height; y += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    redraw() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.drawGrid();
        this.drawFrame();
        // Note: In a full implementation, you'd redraw all saved paths here
    }

    saveState() {
        this.undoStack.push(this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height));
    }

    undo() {
        if (this.undoStack.length > 0) {
            const imageData = this.undoStack.pop();
            this.ctx.putImageData(imageData, 0, 0);
        }
    }

    clearCanvas() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.usedColors.clear();
        this.undoStack = [];
        this.redraw();
        this.updateStats();
    }

    toggleEpoxyEffect() {
        const overlay = document.getElementById('canvas-overlay');
        if (this.epoxyEffect) {
            overlay.classList.add('epoxy-effect');
        } else {
            overlay.classList.remove('epoxy-effect');
        }
    }

    updateStats() {
        document.getElementById('canvas-size').textContent = `${this.canvas.width} x ${this.canvas.height}px`;
        document.getElementById('colors-count').textContent = this.usedColors.size;

        // Estimate thread length (very rough approximation)
        const estimatedLength = Math.round(this.usedColors.size * 12); // 12 inches per color average
        document.getElementById('thread-length').textContent = `~${estimatedLength} inches`;

        // Update material list
        const materialList = document.getElementById('material-list');
        materialList.innerHTML = '<li>Canvas with frame (800x600px equivalent)</li><li>Epoxy coating</li>';

        this.usedColors.forEach(color => {
            const colorInfo = getAllColors().find(c => c.hex === color);
            const li = document.createElement('li');
            li.innerHTML = `Thread: ${colorInfo ? colorInfo.name + ' (' + colorInfo.brand + ')' : color}`;
            materialList.appendChild(li);
        });
    }

    saveDesign() {
        const designData = {
            imageData: this.canvas.toDataURL(),
            colors: Array.from(this.usedColors),
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('embroidery-design', JSON.stringify(designData));
        alert('Design saved successfully!');
    }

    loadDesign() {
        const savedDesign = localStorage.getItem('embroidery-design');
        if (savedDesign) {
            const designData = JSON.parse(savedDesign);
            const img = new Image();
            img.onload = () => {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.drawImage(img, 0, 0);
                this.usedColors = new Set(designData.colors);
                this.updateStats();
            };
            img.src = designData.imageData;
        } else {
            alert('No saved design found!');
        }
    }

    exportImage() {
        const link = document.createElement('a');
        link.download = `embroidery-design-${Date.now()}.png`;
        link.href = this.canvas.toDataURL();
        link.click();
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new EmbroideryDesigner();
});
