// Main application class
class EmbroideryDesigner {
    constructor() {
        this.canvas = document.getElementById('main-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.patternGenerator = new PatternGenerator(this.canvas, this.ctx);
        this.designInspiration = new DesignInspiration();
        this.aiDesignEngine = new AIDesignEngine(this.canvas, this.ctx, this.patternGenerator);
        this.aiAPIIntegration = new AIAPIIntegration(this.canvas, this.patternGenerator, getAllColors());
        this.aiDesignParser = new AIDesignParser(this.canvas, this.ctx, this.patternGenerator);
        
        // Application state
        this.currentTool = 'line';
        this.currentColor = '#DC143C';
        this.threadWidth = 3;
        this.isDrawing = false;
        this.showGrid = false;
        this.showFrame = true;
        this.epoxyEffect = false;
        
        // Drawing state
        this.startX = 0;
        this.startY = 0;
        this.paths = [];
        this.undoStack = [];
        this.usedColors = new Set();
        this.currentInspiration = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.populateColorPalette();
        this.drawFrame();
        this.updateStats();
        this.loadAPIKey();
    }

    setupEventListeners() {
        // Canvas events
        this.canvas.addEventListener('mousedown', this.startDrawing.bind(this));
        this.canvas.addEventListener('mousemove', this.draw.bind(this));
        this.canvas.addEventListener('mouseup', this.stopDrawing.bind(this));
        this.canvas.addEventListener('mouseout', this.stopDrawing.bind(this));

        // Tool selection
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectTool(e.target.dataset.tool);
            });
        });

        // Color selection
        document.getElementById('custom-color').addEventListener('change', (e) => {
            this.currentColor = e.target.value;
        });

        // Thread width
        const widthSlider = document.getElementById('thread-width');
        widthSlider.addEventListener('input', (e) => {
            this.threadWidth = parseInt(e.target.value);
            document.getElementById('width-display').textContent = `${this.threadWidth}px`;
        });

        // Canvas settings
        document.getElementById('show-grid').addEventListener('change', (e) => {
            this.showGrid = e.target.checked;
            this.redraw();
        });

        document.getElementById('show-frame').addEventListener('change', (e) => {
            this.showFrame = e.target.checked;
            this.redraw();
        });

        document.getElementById('epoxy-effect').addEventListener('change', (e) => {
            this.epoxyEffect = e.target.checked;
            this.toggleEpoxyEffect();
        });

        // Action buttons
        document.getElementById('clear-canvas').addEventListener('click', () => {
            this.clearCanvas();
        });

        document.getElementById('undo').addEventListener('click', () => {
            this.undo();
        });

        document.getElementById('save-design').addEventListener('click', () => {
            this.saveDesign();
        });

        document.getElementById('load-design').addEventListener('click', () => {
            this.loadDesign();
        });

        document.getElementById('export-image').addEventListener('click', () => {
            this.exportImage();
        });

        // Pattern buttons
        document.getElementById('spiral-pattern').addEventListener('click', () => {
            this.drawPattern('spiral');
        });

        document.getElementById('wave-pattern').addEventListener('click', () => {
            this.drawPattern('wave');
        });

        document.getElementById('mandala-pattern').addEventListener('click', () => {
            this.drawPattern('mandala');
        });

        document.getElementById('geometric-pattern').addEventListener('click', () => {
            this.drawPattern('geometric');
        });

        // Inspiration buttons
        document.getElementById('random-idea').addEventListener('click', () => {
            this.showRandomInspiration();
        });

        document.getElementById('pattern-combo').addEventListener('click', () => {
            this.showPatternCombo();
        });

        document.getElementById('show-themes').addEventListener('click', () => {
            this.toggleThemeBrowser();
        });

        // AI Design Generator buttons
        document.getElementById('generate-ai-design').addEventListener('click', () => {
            this.generateAIDesign();
        });

        document.getElementById('save-api-key').addEventListener('click', () => {
            this.saveAPIKey();
        });
    }

    populateColorPalette() {
        const palette = document.getElementById('color-palette');
        const colors = getAllColors();
        
        colors.forEach((color, index) => {
            const swatch = document.createElement('div');
            swatch.className = 'color-swatch';
            swatch.style.backgroundColor = color.hex;
            swatch.title = `${color.name} (${color.brand})`;
            swatch.addEventListener('click', () => {
                this.selectColor(color.hex);
                document.querySelectorAll('.color-swatch').forEach(s => s.classList.remove('active'));
                swatch.classList.add('active');
            });
            
            if (index === 0) {
                swatch.classList.add('active');
            }
            
            palette.appendChild(swatch);
        });
    }

    selectTool(tool) {
        this.currentTool = tool;
        document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-tool="${tool}"]`).classList.add('active');
    }

    selectColor(color) {
        this.currentColor = color;
        this.usedColors.add(color);
        this.updateStats();
    }

    startDrawing(e) {
        this.isDrawing = true;
        const rect = this.canvas.getBoundingClientRect();
        this.startX = e.clientX - rect.left;
        this.startY = e.clientY - rect.top;
        this.lastX = null;
        this.lastY = null;

        this.saveState();
    }

    draw(e) {
        if (!this.isDrawing) return;

        const rect = this.canvas.getBoundingClientRect();
        const currentX = e.clientX - rect.left;
        const currentY = e.clientY - rect.top;

        this.ctx.strokeStyle = this.currentColor;
        this.ctx.lineWidth = this.threadWidth;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';

        switch (this.currentTool) {
            case 'line':
                // For continuous line drawing
                if (!this.lastX || !this.lastY) {
                    this.lastX = this.startX;
                    this.lastY = this.startY;
                }
                this.drawLine(this.lastX, this.lastY, currentX, currentY);
                this.lastX = currentX;
                this.lastY = currentY;
                break;
            case 'curve':
                this.drawCurve(this.startX, this.startY, currentX, currentY);
                break;
        }
    }

    stopDrawing(e) {
        if (!this.isDrawing) return;
        
        this.isDrawing = false;
        const rect = this.canvas.getBoundingClientRect();
        const endX = e.clientX - rect.left;
        const endY = e.clientY - rect.top;
        
        switch (this.currentTool) {
            case 'circle':
                this.drawCircle(this.startX, this.startY, endX, endY);
                break;
            case 'rectangle':
                this.drawRectangle(this.startX, this.startY, endX, endY);
                break;
        }
        
        this.updateStats();
    }

    drawLine(x1, y1, x2, y2) {
        this.ctx.beginPath();
        this.ctx.moveTo(x1, y1);
        this.ctx.lineTo(x2, y2);
        this.ctx.stroke();
    }

    drawCurve(x1, y1, x2, y2) {
        this.ctx.beginPath();
        this.ctx.moveTo(x1, y1);
        const controlX = (x1 + x2) / 2;
        const controlY = y1 - Math.abs(x2 - x1) / 4;
        this.ctx.quadraticCurveTo(controlX, controlY, x2, y2);
        this.ctx.stroke();
    }

    drawCircle(centerX, centerY, edgeX, edgeY) {
        const radius = Math.sqrt((edgeX - centerX) ** 2 + (edgeY - centerY) ** 2);
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        this.ctx.stroke();
    }

    drawRectangle(x1, y1, x2, y2) {
        this.ctx.beginPath();
        this.ctx.rect(x1, y1, x2 - x1, y2 - y1);
        this.ctx.stroke();
    }

    drawPattern(patternType) {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        switch (patternType) {
            case 'spiral':
                this.patternGenerator.drawSpiral(centerX, centerY, 100, 3, this.currentColor, this.threadWidth);
                break;
            case 'wave':
                this.patternGenerator.drawWave(50, centerY, this.canvas.width - 50, centerY, 30, 2, this.currentColor, this.threadWidth);
                break;
            case 'mandala':
                this.patternGenerator.drawMandala(centerX, centerY, 120, 8, this.currentColor, this.threadWidth);
                break;
            case 'geometric':
                this.patternGenerator.drawGeometric(centerX, centerY, 100, 6, 3, this.currentColor, this.threadWidth);
                break;
        }
        
        this.usedColors.add(this.currentColor);
        this.updateStats();
    }

    drawFrame() {
        if (!this.showFrame) return;
        
        this.ctx.strokeStyle = '#8B4513';
        this.ctx.lineWidth = 6;
        this.ctx.strokeRect(3, 3, this.canvas.width - 6, this.canvas.height - 6);
    }

    drawGrid() {
        if (!this.showGrid) return;
        
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.lineWidth = 1;
        
        const spacing = 20;
        
        for (let x = 0; x <= this.canvas.width; x += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = 0; y <= this.canvas.height; y += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    redraw() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.drawGrid();
        this.drawFrame();
        // Note: In a full implementation, you'd redraw all saved paths here
    }

    saveState() {
        this.undoStack.push(this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height));
    }

    undo() {
        if (this.undoStack.length > 0) {
            const imageData = this.undoStack.pop();
            this.ctx.putImageData(imageData, 0, 0);
        }
    }

    clearCanvas() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.usedColors.clear();
        this.undoStack = [];
        this.redraw();
        this.updateStats();
    }

    toggleEpoxyEffect() {
        const overlay = document.getElementById('canvas-overlay');
        if (this.epoxyEffect) {
            overlay.classList.add('epoxy-effect');
        } else {
            overlay.classList.remove('epoxy-effect');
        }
    }

    updateStats() {
        document.getElementById('canvas-size').textContent = `${this.canvas.width} x ${this.canvas.height}px`;
        document.getElementById('colors-count').textContent = this.usedColors.size;

        // Estimate thread length (very rough approximation)
        const estimatedLength = Math.round(this.usedColors.size * 12); // 12 inches per color average
        document.getElementById('thread-length').textContent = `~${estimatedLength} inches`;

        // Update material list
        const materialList = document.getElementById('material-list');
        materialList.innerHTML = '<li>Canvas with frame (800x600px equivalent)</li><li>Epoxy coating</li>';

        this.usedColors.forEach(color => {
            const colorInfo = getAllColors().find(c => c.hex === color);
            const li = document.createElement('li');
            li.innerHTML = `Thread: ${colorInfo ? colorInfo.name + ' (' + colorInfo.brand + ')' : color}`;
            materialList.appendChild(li);
        });
    }

    saveDesign() {
        const designData = {
            imageData: this.canvas.toDataURL(),
            colors: Array.from(this.usedColors),
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('embroidery-design', JSON.stringify(designData));
        alert('Design saved successfully!');
    }

    loadDesign() {
        const savedDesign = localStorage.getItem('embroidery-design');
        if (savedDesign) {
            const designData = JSON.parse(savedDesign);
            const img = new Image();
            img.onload = () => {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.drawImage(img, 0, 0);
                this.usedColors = new Set(designData.colors);
                this.updateStats();
            };
            img.src = designData.imageData;
        } else {
            alert('No saved design found!');
        }
    }

    exportImage() {
        const link = document.createElement('a');
        link.download = `embroidery-design-${Date.now()}.png`;
        link.href = this.canvas.toDataURL();
        link.click();
    }

    // Inspiration Methods
    showRandomInspiration() {
        this.currentInspiration = this.designInspiration.getRandomIdea();
        this.displayInspiration(this.currentInspiration);
        this.showInspirationSection();
    }

    showPatternCombo() {
        this.currentInspiration = this.designInspiration.generateRandomPatternCombo();
        this.displayInspiration(this.currentInspiration);
        this.showInspirationSection();
    }

    displayInspiration(inspiration) {
        const container = document.getElementById('current-inspiration');

        container.innerHTML = `
            <div class="inspiration-title">${inspiration.title}</div>
            <div class="inspiration-description">${inspiration.description}</div>
            ${inspiration.difficulty ? `<div class="inspiration-difficulty difficulty-${inspiration.difficulty}">${inspiration.difficulty.toUpperCase()}</div>` : ''}
            <div class="inspiration-colors">
                ${inspiration.colors ? inspiration.colors.map(color =>
                    `<div class="inspiration-color-swatch" style="background-color: ${color}" title="${color}"></div>`
                ).join('') : ''}
                ${inspiration.colorCombination ? inspiration.colorCombination.colors.map(color =>
                    `<div class="inspiration-color-swatch" style="background-color: ${color}" title="${color}"></div>`
                ).join('') : ''}
            </div>
            ${inspiration.patterns ? `<div class="inspiration-patterns">Patterns: ${inspiration.patterns.join(', ')}</div>` : ''}
            ${inspiration.instructions ? `<div class="inspiration-instructions">${inspiration.instructions}</div>` : ''}
            ${inspiration.colorCombination ? `<div class="color-combo-info"><strong>${inspiration.colorCombination.name}</strong> - ${inspiration.colorCombination.mood}</div>` : ''}
        `;

        // Update the actions HTML to include draw pattern button
        const actionsContainer = document.querySelector('.inspiration-actions');
        if (actionsContainer) {
            actionsContainer.innerHTML = `
                <button id="apply-inspiration" class="apply-btn">Apply Colors</button>
                <button id="draw-inspiration" class="apply-btn">Draw Pattern</button>
                <button id="new-inspiration" class="apply-btn">New Idea</button>
            `;
        }

        // Setup apply buttons
        this.setupInspirationActions();
    }

    setupInspirationActions() {
        // Remove existing listeners to prevent duplicates
        const applyBtn = document.getElementById('apply-inspiration');
        const newBtn = document.getElementById('new-inspiration');

        if (applyBtn) {
            applyBtn.replaceWith(applyBtn.cloneNode(true));
            document.getElementById('apply-inspiration').addEventListener('click', () => {
                this.applyInspirationColors();
            });
        }

        if (newBtn) {
            newBtn.replaceWith(newBtn.cloneNode(true));
            document.getElementById('new-inspiration').addEventListener('click', () => {
                this.showRandomInspiration();
            });
        }

        const drawBtn = document.getElementById('draw-inspiration');
        if (drawBtn) {
            drawBtn.replaceWith(drawBtn.cloneNode(true));
            document.getElementById('draw-inspiration').addEventListener('click', () => {
                this.drawInspirationPattern();
            });
        }
    }

    applyInspirationColors() {
        if (!this.currentInspiration) return;

        const colors = this.currentInspiration.colors || this.currentInspiration.colorCombination?.colors || [];
        if (colors.length > 0) {
            // Set the first color as current
            this.selectColor(colors[0]);

            // Update color palette to highlight matching colors
            const allColors = getAllColors();
            document.querySelectorAll('.color-swatch').forEach(swatch => {
                swatch.classList.remove('active');
                const swatchColor = swatch.style.backgroundColor;

                // Convert RGB to hex for comparison
                const rgbMatch = swatchColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
                if (rgbMatch) {
                    const hex = '#' +
                        parseInt(rgbMatch[1]).toString(16).padStart(2, '0') +
                        parseInt(rgbMatch[2]).toString(16).padStart(2, '0') +
                        parseInt(rgbMatch[3]).toString(16).padStart(2, '0');

                    if (colors.includes(hex.toUpperCase()) || colors.includes(hex.toLowerCase())) {
                        swatch.classList.add('active');
                    }
                }
            });

            // Set custom color to first color
            document.getElementById('custom-color').value = colors[0];

            // Show success message
            this.showMessage(`Applied ${colors.length} colors from inspiration!`);
        }
    }

    showMessage(message) {
        // Create a temporary message element
        const messageEl = document.createElement('div');
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 1000;
            font-size: 0.9em;
        `;

        document.body.appendChild(messageEl);

        setTimeout(() => {
            messageEl.remove();
        }, 3000);
    }

    drawInspirationPattern() {
        if (!this.currentInspiration) return;

        const patterns = this.currentInspiration.patterns || [];
        const colors = this.currentInspiration.colors || this.currentInspiration.colorCombination?.colors || [];

        if (patterns.length === 0) {
            this.showMessage('No patterns defined for this inspiration');
            return;
        }

        this.saveState(); // Save for undo

        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;

        patterns.forEach((pattern, index) => {
            const color = colors[index % colors.length] || this.currentColor;
            this.selectColor(color);

            // Offset patterns slightly if multiple
            const offsetX = patterns.length > 1 ? (index - patterns.length/2) * 50 : 0;
            const offsetY = patterns.length > 1 ? (index - patterns.length/2) * 30 : 0;

            switch (pattern) {
                case 'spiral':
                    this.patternGenerator.drawSpiral(centerX + offsetX, centerY + offsetY, 80, 2.5, color, this.threadWidth);
                    break;
                case 'wave':
                    this.patternGenerator.drawWave(
                        centerX - 100 + offsetX, centerY + offsetY,
                        centerX + 100 + offsetX, centerY + offsetY,
                        25, 2, color, this.threadWidth
                    );
                    break;
                case 'mandala':
                    this.patternGenerator.drawMandala(centerX + offsetX, centerY + offsetY, 90, 8, color, this.threadWidth);
                    break;
                case 'geometric':
                    this.patternGenerator.drawGeometric(centerX + offsetX, centerY + offsetY, 80, 6, 3, color, this.threadWidth);
                    break;
                case 'flower':
                    this.patternGenerator.drawFlower(centerX + offsetX, centerY + offsetY, 60, 6, color, this.threadWidth);
                    break;
                case 'line':
                    // Draw radiating lines
                    for (let i = 0; i < 8; i++) {
                        const angle = (i * Math.PI * 2) / 8;
                        const endX = centerX + offsetX + 80 * Math.cos(angle);
                        const endY = centerY + offsetY + 80 * Math.sin(angle);
                        this.drawLine(centerX + offsetX, centerY + offsetY, endX, endY);
                    }
                    break;
                case 'circle':
                    this.drawCircle(centerX + offsetX, centerY + offsetY, centerX + offsetX + 60, centerY + offsetY);
                    break;
                case 'curve':
                    // Draw flowing curves
                    for (let i = 0; i < 4; i++) {
                        const startX = centerX + offsetX - 60 + i * 40;
                        const startY = centerY + offsetY - 40;
                        const endX = centerX + offsetX - 40 + i * 40;
                        const endY = centerY + offsetY + 40;
                        this.drawCurve(startX, startY, endX, endY);
                    }
                    break;
            }

            this.usedColors.add(color);
        });

        this.updateStats();
        this.showMessage(`Drew ${patterns.join(' + ')} pattern with inspiration colors!`);
    }

    showInspirationSection() {
        document.getElementById('inspiration-section').style.display = 'block';
        document.getElementById('theme-browser').style.display = 'none';
    }

    toggleThemeBrowser() {
        const themeBrowser = document.getElementById('theme-browser');
        const inspirationSection = document.getElementById('inspiration-section');

        if (themeBrowser.style.display === 'none') {
            themeBrowser.style.display = 'block';
            inspirationSection.style.display = 'none';
            this.setupThemeBrowser();
        } else {
            themeBrowser.style.display = 'none';
        }
    }

    setupThemeBrowser() {
        const themes = this.designInspiration.getAllThemes();
        const tabsContainer = document.getElementById('theme-tabs');
        const ideasContainer = document.getElementById('theme-ideas');

        // Create theme tabs
        tabsContainer.innerHTML = '';
        Object.keys(themes).forEach((themeKey, index) => {
            const theme = themes[themeKey];
            const tab = document.createElement('div');
            tab.className = `theme-tab ${index === 0 ? 'active' : ''}`;
            tab.textContent = `${theme.icon} ${theme.name}`;
            tab.addEventListener('click', () => {
                this.showThemeIdeas(themeKey);
                document.querySelectorAll('.theme-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
            });
            tabsContainer.appendChild(tab);
        });

        // Show first theme by default
        this.showThemeIdeas(Object.keys(themes)[0]);
    }

    showThemeIdeas(themeKey) {
        const ideas = this.designInspiration.getIdeasByTheme(themeKey);
        const container = document.getElementById('theme-ideas');

        container.innerHTML = ideas.map(idea => `
            <div class="theme-idea-item" data-idea='${JSON.stringify({...idea, theme: themeKey})}'>
                <div class="theme-idea-title">${idea.title}</div>
                <div class="theme-idea-description">${idea.description}</div>
                <div class="theme-idea-meta">
                    <span class="inspiration-difficulty difficulty-${idea.difficulty}">${idea.difficulty}</span>
                    <div class="color-combo-preview">
                        ${idea.colors.slice(0, 4).map(color =>
                            `<div class="mini-swatch" style="background-color: ${color}"></div>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `).join('');

        // Add click handlers to idea items
        container.querySelectorAll('.theme-idea-item').forEach(item => {
            item.addEventListener('click', () => {
                const ideaData = JSON.parse(item.dataset.idea);
                this.currentInspiration = ideaData;
                this.displayInspiration(ideaData);
                this.showInspirationSection();
            });
        });
    }

    // AI Design Generation Methods
    loadAPIKey() {
        const savedKey = this.aiAPIIntegration.loadAPIKey();
        const isUsingEnv = this.aiAPIIntegration.isUsingEnvironmentAPIKey();

        if (savedKey && !isUsingEnv) {
            document.getElementById('gemini-api-key').value = savedKey;
        }

        this.updateAPIKeyStatus();
    }

    updateAPIKeyStatus() {
        const statusElement = document.getElementById('api-key-status');
        const isUsingEnv = this.aiAPIIntegration.isUsingEnvironmentAPIKey();
        const hasKey = !!this.aiAPIIntegration.loadAPIKey();

        if (isUsingEnv) {
            statusElement.textContent = 'Using API key from environment (.env file)';
            statusElement.className = 'api-key-status env-configured';
            document.getElementById('gemini-api-key').disabled = true;
            document.getElementById('gemini-api-key').placeholder = 'API key configured via environment';
            document.getElementById('save-api-key').disabled = true;
        } else if (hasKey) {
            statusElement.textContent = 'API key saved in browser storage';
            statusElement.className = 'api-key-status local-storage';
            document.getElementById('gemini-api-key').disabled = false;
            document.getElementById('save-api-key').disabled = false;
        } else {
            statusElement.textContent = 'API key not configured';
            statusElement.className = 'api-key-status not-configured';
            document.getElementById('gemini-api-key').disabled = false;
            document.getElementById('save-api-key').disabled = false;
        }
    }

    saveAPIKey() {
        const apiKey = document.getElementById('gemini-api-key').value.trim();
        if (apiKey) {
            this.aiAPIIntegration.saveAPIKey(apiKey);
            this.updateAPIKeyStatus();
            this.showMessage('API key saved successfully!');
        } else {
            this.showMessage('Please enter a valid API key.');
        }
    }

    async generateAIDesign() {
        const generateBtn = document.getElementById('generate-ai-design');
        const originalText = generateBtn.textContent;

        try {
            // Disable button and show loading state
            generateBtn.disabled = true;
            generateBtn.textContent = '🤖 Generating...';

            // Get settings from UI
            const style = document.getElementById('ai-style').value;
            const complexity = document.getElementById('ai-complexity').value;
            const theme = document.getElementById('ai-theme').value;
            const userPrompt = document.getElementById('ai-prompt').value.trim();

            if (!userPrompt) {
                throw new Error('Please enter a design prompt.');
            }

            // Save current state for undo
            this.saveState();

            this.showMessage('Generating AI design... This may take a few moments.');

            let designData;
            try {
                // Try to generate with AI API
                designData = await this.aiAPIIntegration.generateDesign(userPrompt, style, complexity, theme);
            } catch (apiError) {
                console.warn('AI API failed, using fallback:', apiError.message);
                this.showMessage('AI API unavailable, using fallback design generator...');

                // Use fallback design generation
                designData = this.aiAPIIntegration.generateFallbackDesign(style, complexity, theme);
            }

            // Render the design
            const renderResult = this.aiDesignParser.renderDesignFromAI(designData, this.usedColors);

            if (renderResult.success) {
                // Update used colors
                renderResult.colorsUsed.forEach(color => this.usedColors.add(color));

                // Update stats and display info
                this.updateStats();
                this.displayAIDesignInfo(designData);

                this.showMessage(`AI design generated successfully! Style: ${style}, Complexity: ${complexity}`);
            } else {
                throw new Error(renderResult.error || 'Failed to render AI design');
            }

        } catch (error) {
            console.error('AI Design Generation Error:', error);
            this.showMessage(`Error: ${error.message}`);

            // Restore canvas if there was an error
            if (this.undoStack.length > 0) {
                this.undo();
            }
        } finally {
            // Re-enable button
            generateBtn.disabled = false;
            generateBtn.textContent = originalText;
        }
    }



    displayAIDesignInfo(designData) {
        const metadata = designData.metadata;
        const design = designData.design;

        // Update the info panel with AI design information
        const infoPanel = document.querySelector('.info-panel');

        // Create or update AI info section
        let aiInfoSection = document.getElementById('ai-design-info');
        if (!aiInfoSection) {
            aiInfoSection = document.createElement('div');
            aiInfoSection.id = 'ai-design-info';
            aiInfoSection.className = 'ai-info-section';

            // Find a safe insertion point
            const designStats = document.getElementById('design-stats');
            if (designStats) {
                // Insert after design stats
                if (designStats.nextSibling) {
                    infoPanel.insertBefore(aiInfoSection, designStats.nextSibling);
                } else {
                    infoPanel.appendChild(aiInfoSection);
                }
            } else {
                // Fallback: insert at the beginning of info panel
                const firstChild = infoPanel.firstChild;
                if (firstChild) {
                    infoPanel.insertBefore(aiInfoSection, firstChild);
                } else {
                    infoPanel.appendChild(aiInfoSection);
                }
            }
        }

        aiInfoSection.innerHTML = `
            <h4>🤖 AI Design Analysis</h4>
            <div class="ai-stats">
                <p><strong>Title:</strong> ${design.title || 'AI Generated Design'}</p>
                <p><strong>Style:</strong> ${design.style || 'Mixed'}</p>
                <p><strong>Complexity:</strong> ${metadata.complexity}</p>
                <p><strong>Difficulty:</strong> ${metadata.difficulty}</p>
                <p><strong>Estimated Time:</strong> ${metadata.estimatedTime}</p>
                <p><strong>Description:</strong> ${metadata.description}</p>
                <p><strong>Main Patterns:</strong> ${design.mainPatterns?.length || 0}</p>
                <p><strong>Focus Points:</strong> ${design.focusPoints?.length || 0}</p>
                <p><strong>Detail Elements:</strong> ${design.detailElements?.length || 0}</p>
            </div>
        `;
    }


}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new EmbroideryDesigner();
});
