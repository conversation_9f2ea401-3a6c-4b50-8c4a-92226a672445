/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.app-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.app-header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.app-header p {
    font-size: 1.2em;
    opacity: 0.9;
}

/* Main content layout */
.main-content {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 20px;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Toolbar */
.toolbar {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    height: fit-content;
}

.tool-section {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.tool-section:last-child {
    border-bottom: none;
}

.tool-section h3 {
    font-size: 1em;
    margin-bottom: 10px;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Buttons */
.tool-btn, .action-btn, .pattern-btn {
    width: 100%;
    padding: 10px;
    margin-bottom: 5px;
    border: 2px solid #dee2e6;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.tool-btn:hover, .action-btn:hover, .pattern-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.tool-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.action-btn {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.action-btn:hover {
    background: #218838;
}

.pattern-btn {
    background: #6f42c1;
    color: white;
    border-color: #6f42c1;
}

.pattern-btn:hover {
    background: #5a32a3;
}

.inspiration-btn {
    background: #17a2b8;
    color: white;
    border-color: #17a2b8;
}

.inspiration-btn:hover {
    background: #138496;
}

.ai-btn {
    background: #6f42c1;
    color: white;
    border-color: #6f42c1;
}

.ai-btn:hover {
    background: #5a32a3;
}

/* AI Settings */
.tool-section label {
    display: block;
    margin-top: 10px;
    margin-bottom: 5px;
    font-size: 0.9em;
    font-weight: bold;
    color: #495057;
}

.tool-section select {
    width: 100%;
    padding: 8px;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    background: white;
    font-size: 0.9em;
    margin-bottom: 10px;
}

.tool-section select:focus {
    outline: none;
    border-color: #6f42c1;
}

/* API Key Configuration */
.api-key-container {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.api-key-container input {
    flex: 1;
    padding: 8px;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    font-size: 0.9em;
}

.api-key-container input:focus {
    outline: none;
    border-color: #6f42c1;
}

.save-key-btn {
    padding: 8px 12px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
}

.save-key-btn:hover {
    background: #218838;
}

.api-help {
    display: block;
    font-size: 0.8em;
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.3;
}

.api-help a {
    color: #007bff;
    text-decoration: none;
}

.api-help a:hover {
    text-decoration: underline;
}

/* API Key Status */
.api-key-status {
    margin: 5px 0;
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 0.8em;
    font-weight: bold;
}

.api-key-status.env-configured {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.api-key-status.env-configured::before {
    content: "🔒 ";
}

.api-key-status.local-storage {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.api-key-status.local-storage::before {
    content: "💾 ";
}

.api-key-status.not-configured {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.api-key-status.not-configured::before {
    content: "⚠️ ";
}

/* AI Prompt Textarea */
.tool-section textarea {
    width: 100%;
    padding: 8px;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    font-size: 0.9em;
    font-family: inherit;
    resize: vertical;
    margin-bottom: 15px;
}

.tool-section textarea:focus {
    outline: none;
    border-color: #6f42c1;
}

/* AI Generate Button */
.ai-generate-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1em;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(111, 66, 193, 0.3);
}

.ai-generate-btn:hover {
    background: linear-gradient(135deg, #5a32a3, #4c2a85);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(111, 66, 193, 0.4);
}

.ai-generate-btn:active {
    transform: translateY(0);
}

.ai-generate-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.apply-btn {
    background: #28a745;
    color: white;
    border: 2px solid #28a745;
    border-radius: 5px;
    padding: 8px 12px;
    margin: 5px;
    cursor: pointer;
    font-size: 0.8em;
    transition: all 0.3s ease;
}

.apply-btn:hover {
    background: #218838;
}

/* Color palette */
.color-palette {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 5px;
    margin-bottom: 10px;
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: transform 0.2s ease;
}

.color-swatch:hover {
    transform: scale(1.1);
}

.color-swatch.active {
    border-color: #007bff;
    box-shadow: 0 0 0 2px #007bff;
}

/* Thread width control */
#thread-width {
    width: 100%;
    margin-bottom: 5px;
}

#width-display {
    font-weight: bold;
    color: #007bff;
}

/* Custom color input */
#custom-color {
    width: 50px;
    height: 30px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

/* Canvas container */
.canvas-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    min-height: 600px;
}

#main-canvas {
    border: 3px solid #8B4513;
    border-radius: 5px;
    background: #FFFEF7;
    box-shadow: 
        inset 0 0 20px rgba(139, 69, 19, 0.1),
        0 5px 15px rgba(0,0,0,0.2);
    cursor: crosshair;
}

.canvas-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    pointer-events: none;
    border-radius: 5px;
}

.epoxy-effect {
    background: linear-gradient(
        45deg,
        rgba(255,255,255,0.1) 0%,
        rgba(255,255,255,0.3) 50%,
        rgba(255,255,255,0.1) 100%
    );
}

/* Info panel */
.info-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    height: fit-content;
}

.info-panel h3, .info-panel h4 {
    color: #495057;
    margin-bottom: 10px;
}

.info-panel h4 {
    margin-top: 20px;
    font-size: 1em;
}

#design-stats p {
    margin-bottom: 5px;
    font-size: 0.9em;
}

#design-stats span {
    font-weight: bold;
    color: #007bff;
}

#material-list, #instructions {
    font-size: 0.9em;
    line-height: 1.4;
}

#material-list li, #instructions li {
    margin-bottom: 5px;
}

/* Responsive design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 200px 1fr 250px;
    }
}

@media (max-width: 900px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }
    
    .toolbar {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
}

/* Grid overlay */
.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0.3;
    background-image:
        linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Inspiration Section Styles */
.inspiration-section {
    margin: 20px 0;
    padding: 15px;
    background: #e9ecef;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
}

.current-inspiration {
    margin-bottom: 15px;
}

.inspiration-title {
    font-weight: bold;
    color: #17a2b8;
    margin-bottom: 5px;
}

.inspiration-description {
    font-size: 0.9em;
    margin-bottom: 10px;
    line-height: 1.4;
}

.inspiration-difficulty {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    margin-bottom: 10px;
}

.difficulty-beginner {
    background: #d4edda;
    color: #155724;
}

.difficulty-intermediate {
    background: #fff3cd;
    color: #856404;
}

.difficulty-advanced {
    background: #f8d7da;
    color: #721c24;
}

.inspiration-colors {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.inspiration-color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.inspiration-patterns {
    font-size: 0.8em;
    color: #6c757d;
    margin-bottom: 10px;
}

.inspiration-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

/* Theme Browser Styles */
.theme-browser {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #6f42c1;
}

.theme-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 15px;
}

.theme-tab {
    padding: 8px 12px;
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.8em;
    transition: all 0.3s ease;
}

.theme-tab:hover {
    background: #e9ecef;
}

.theme-tab.active {
    background: #6f42c1;
    color: white;
    border-color: #6f42c1;
}

.theme-ideas {
    max-height: 300px;
    overflow-y: auto;
}

.theme-idea-item {
    padding: 10px;
    margin-bottom: 10px;
    background: white;
    border-radius: 5px;
    border-left: 3px solid #6f42c1;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-idea-item:hover {
    background: #f8f9fa;
    transform: translateX(2px);
}

.theme-idea-title {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.theme-idea-description {
    font-size: 0.8em;
    color: #6c757d;
    margin-bottom: 8px;
}

.theme-idea-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.7em;
}

.color-combo-preview {
    display: flex;
    gap: 2px;
}

.color-combo-preview .mini-swatch {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 1px solid white;
}

/* AI Design Info Styles */
.ai-info-section {
    margin: 20px 0;
    padding: 15px;
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    color: white;
    border-radius: 8px;
    border-left: 4px solid #fff;
}

.ai-info-section h4 {
    color: white;
    margin-bottom: 10px;
}

.ai-stats p {
    margin-bottom: 5px;
    font-size: 0.9em;
}

.ai-stats strong {
    color: #fff;
}

.optimization-section {
    margin: 20px 0;
    padding: 15px;
    background: #e8f5e8;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.optimization-section h4 {
    color: #28a745;
    margin-bottom: 15px;
}

.stat-bar {
    margin-bottom: 15px;
}

.stat-bar label {
    display: block;
    font-size: 0.9em;
    font-weight: bold;
    margin-bottom: 5px;
    color: #495057;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 10px;
    transition: width 0.5s ease;
}

.stat-bar span {
    font-size: 0.8em;
    font-weight: bold;
    color: #28a745;
}

.optimization-tip {
    font-size: 0.9em;
    font-style: italic;
    color: #6c757d;
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    border-left: 3px solid #17a2b8;
}
