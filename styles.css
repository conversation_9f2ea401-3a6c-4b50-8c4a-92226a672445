/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.app-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.app-header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.app-header p {
    font-size: 1.2em;
    opacity: 0.9;
}

/* Main content layout */
.main-content {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 20px;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Toolbar */
.toolbar {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    height: fit-content;
}

.tool-section {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.tool-section:last-child {
    border-bottom: none;
}

.tool-section h3 {
    font-size: 1em;
    margin-bottom: 10px;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Buttons */
.tool-btn, .action-btn, .pattern-btn {
    width: 100%;
    padding: 10px;
    margin-bottom: 5px;
    border: 2px solid #dee2e6;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.tool-btn:hover, .action-btn:hover, .pattern-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.tool-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.action-btn {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.action-btn:hover {
    background: #218838;
}

.pattern-btn {
    background: #6f42c1;
    color: white;
    border-color: #6f42c1;
}

.pattern-btn:hover {
    background: #5a32a3;
}

/* Color palette */
.color-palette {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 5px;
    margin-bottom: 10px;
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: transform 0.2s ease;
}

.color-swatch:hover {
    transform: scale(1.1);
}

.color-swatch.active {
    border-color: #007bff;
    box-shadow: 0 0 0 2px #007bff;
}

/* Thread width control */
#thread-width {
    width: 100%;
    margin-bottom: 5px;
}

#width-display {
    font-weight: bold;
    color: #007bff;
}

/* Custom color input */
#custom-color {
    width: 50px;
    height: 30px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

/* Canvas container */
.canvas-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    min-height: 600px;
}

#main-canvas {
    border: 3px solid #8B4513;
    border-radius: 5px;
    background: #FFFEF7;
    box-shadow: 
        inset 0 0 20px rgba(139, 69, 19, 0.1),
        0 5px 15px rgba(0,0,0,0.2);
    cursor: crosshair;
}

.canvas-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    pointer-events: none;
    border-radius: 5px;
}

.epoxy-effect {
    background: linear-gradient(
        45deg,
        rgba(255,255,255,0.1) 0%,
        rgba(255,255,255,0.3) 50%,
        rgba(255,255,255,0.1) 100%
    );
}

/* Info panel */
.info-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    height: fit-content;
}

.info-panel h3, .info-panel h4 {
    color: #495057;
    margin-bottom: 10px;
}

.info-panel h4 {
    margin-top: 20px;
    font-size: 1em;
}

#design-stats p {
    margin-bottom: 5px;
    font-size: 0.9em;
}

#design-stats span {
    font-weight: bold;
    color: #007bff;
}

#material-list, #instructions {
    font-size: 0.9em;
    line-height: 1.4;
}

#material-list li, #instructions li {
    margin-bottom: 5px;
}

/* Responsive design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 200px 1fr 250px;
    }
}

@media (max-width: 900px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }
    
    .toolbar {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
}

/* Grid overlay */
.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0.3;
    background-image: 
        linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}
