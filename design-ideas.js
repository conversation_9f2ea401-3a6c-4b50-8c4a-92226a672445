// Design Ideas and Inspiration System for Embroidery Thread Art

class DesignInspiration {
    constructor() {
        this.designIdeas = this.initializeDesignIdeas();
        this.colorCombinations = this.initializeColorCombinations();
        this.themes = this.initializeThemes();
    }

    initializeDesignIdeas() {
        return {
            nature: [
                {
                    title: "Flowing River",
                    description: "Curved lines representing water flow with earth tone accents",
                    difficulty: "beginner",
                    patterns: ["wave", "curve"],
                    colors: ["#4169E1", "#87CEEB", "#228B22", "#8B4513"],
                    instructions: "Create flowing wave patterns in blues, add green banks and brown rocks"
                },
                {
                    title: "Sunset Tree Silhouette",
                    description: "Geometric tree with radiating sunset background",
                    difficulty: "intermediate",
                    patterns: ["line", "geometric"],
                    colors: ["#FF8C00", "#FFD700", "#DC143C", "#000000"],
                    instructions: "Draw tree trunk and branches in black, create radiating sunset lines behind"
                },
                {
                    title: "Blooming Mandala Garden",
                    description: "Layered mandala patterns representing flowers in full bloom",
                    difficulty: "advanced",
                    patterns: ["mandala", "flower"],
                    colors: ["#FF69B4", "#FFD700", "#50C878", "#663399"],
                    instructions: "Layer multiple mandala patterns, vary sizes, use flower colors"
                },
                {
                    title: "Mountain Range Layers",
                    description: "Overlapping triangular shapes creating depth and perspective",
                    difficulty: "intermediate",
                    patterns: ["geometric", "line"],
                    colors: ["#663399", "#4169E1", "#87CEEB", "#FFFFFF"],
                    instructions: "Create layered triangular mountains, lighter colors for distance"
                }
            ],
            abstract: [
                {
                    title: "Cosmic Spiral Galaxy",
                    description: "Multiple spirals with metallic accents creating cosmic depth",
                    difficulty: "advanced",
                    patterns: ["spiral", "circle"],
                    colors: ["#663399", "#000080", "#FFD700", "#C0C0C0"],
                    instructions: "Create large central spiral, add smaller spirals and metallic stars"
                },
                {
                    title: "Geometric Harmony",
                    description: "Interlocking geometric shapes in complementary colors",
                    difficulty: "intermediate",
                    patterns: ["geometric", "rectangle"],
                    colors: ["#FF8C00", "#4169E1", "#FFFFFF", "#000000"],
                    instructions: "Create overlapping geometric patterns, use contrasting colors"
                },
                {
                    title: "Flowing Energy Lines",
                    description: "Dynamic curved lines suggesting movement and energy",
                    difficulty: "beginner",
                    patterns: ["curve", "wave"],
                    colors: ["#DC143C", "#FF8C00", "#FFD700", "#FFFFFF"],
                    instructions: "Draw flowing curves from center outward, use warm color gradient"
                },
                {
                    title: "Minimalist Zen Circles",
                    description: "Simple overlapping circles in neutral tones",
                    difficulty: "beginner",
                    patterns: ["circle"],
                    colors: ["#000000", "#C0C0C0", "#FFFFFF", "#8B4513"],
                    instructions: "Create overlapping circles of varying sizes, use minimal color palette"
                }
            ],
            seasonal: [
                {
                    title: "Spring Awakening",
                    description: "Fresh green spirals with colorful flower accents",
                    difficulty: "intermediate",
                    patterns: ["spiral", "flower"],
                    colors: ["#32CD32", "#FF69B4", "#FFD700", "#87CEEB"],
                    instructions: "Create green spiral base, add colorful flower patterns throughout"
                },
                {
                    title: "Autumn Leaf Dance",
                    description: "Swirling patterns in warm autumn colors",
                    difficulty: "intermediate",
                    patterns: ["wave", "curve"],
                    colors: ["#FF8C00", "#B22222", "#FFD700", "#8B4513"],
                    instructions: "Create swirling leaf-like patterns, layer warm autumn colors"
                },
                {
                    title: "Winter Snowflake Mandala",
                    description: "Intricate mandala patterns resembling snowflakes",
                    difficulty: "advanced",
                    patterns: ["mandala", "geometric"],
                    colors: ["#FFFFFF", "#87CEEB", "#C0C0C0", "#4169E1"],
                    instructions: "Create detailed mandala with snowflake-like symmetry"
                },
                {
                    title: "Summer Sun Rays",
                    description: "Radiating lines from center with warm, bright colors",
                    difficulty: "beginner",
                    patterns: ["line", "circle"],
                    colors: ["#FFD700", "#FF8C00", "#DC143C", "#FFFFFF"],
                    instructions: "Draw radiating lines from center circle, use bright summer colors"
                }
            ],
            artistic: [
                {
                    title: "Bohemian Tapestry",
                    description: "Layered patterns with rich, earthy colors",
                    difficulty: "advanced",
                    patterns: ["mandala", "wave", "geometric"],
                    colors: ["#8B4513", "#FF8C00", "#663399", "#FFD700"],
                    instructions: "Layer multiple pattern types, use rich bohemian color palette"
                },
                {
                    title: "Contemporary Lines",
                    description: "Clean, modern geometric patterns with bold contrasts",
                    difficulty: "intermediate",
                    patterns: ["line", "rectangle"],
                    colors: ["#000000", "#FFFFFF", "#DC143C", "#4169E1"],
                    instructions: "Create clean geometric lines, use high contrast colors"
                },
                {
                    title: "Art Deco Elegance",
                    description: "Symmetrical patterns with metallic accents",
                    difficulty: "advanced",
                    patterns: ["geometric", "line"],
                    colors: ["#000000", "#FFD700", "#C0C0C0", "#FFFFFF"],
                    instructions: "Create symmetrical Art Deco patterns, emphasize metallic elements"
                },
                {
                    title: "Minimalist Meditation",
                    description: "Simple, calming patterns in neutral tones",
                    difficulty: "beginner",
                    patterns: ["circle", "line"],
                    colors: ["#F5F5DC", "#D2B48C", "#8B4513", "#FFFFFF"],
                    instructions: "Create simple, balanced patterns, use calming neutral colors"
                }
            ]
        };
    }

    initializeColorCombinations() {
        return [
            {
                name: "Ocean Depths",
                colors: ["#000080", "#4169E1", "#87CEEB", "#FFFFFF"],
                mood: "Calming, Deep",
                bestFor: ["abstract", "nature"]
            },
            {
                name: "Sunset Glow",
                colors: ["#FF8C00", "#FFD700", "#DC143C", "#663399"],
                mood: "Warm, Energetic",
                bestFor: ["nature", "seasonal"]
            },
            {
                name: "Forest Harmony",
                colors: ["#228B22", "#50C878", "#8B4513", "#FFD700"],
                mood: "Natural, Grounding",
                bestFor: ["nature", "artistic"]
            },
            {
                name: "Royal Elegance",
                colors: ["#663399", "#FFD700", "#000000", "#FFFFFF"],
                mood: "Luxurious, Bold",
                bestFor: ["artistic", "abstract"]
            },
            {
                name: "Spring Fresh",
                colors: ["#32CD32", "#FF69B4", "#FFD700", "#87CEEB"],
                mood: "Fresh, Vibrant",
                bestFor: ["seasonal", "nature"]
            },
            {
                name: "Monochrome Modern",
                colors: ["#000000", "#C0C0C0", "#FFFFFF", "#8B4513"],
                mood: "Clean, Sophisticated",
                bestFor: ["artistic", "abstract"]
            }
        ];
    }

    initializeThemes() {
        return {
            nature: {
                name: "Nature Inspired",
                description: "Designs inspired by natural elements, landscapes, and organic forms",
                icon: "🌿",
                keywords: ["organic", "flowing", "earth tones", "natural patterns"]
            },
            abstract: {
                name: "Abstract Expression",
                description: "Non-representational designs focusing on form, color, and movement",
                icon: "🎨",
                keywords: ["geometric", "flowing", "dynamic", "expressive"]
            },
            seasonal: {
                name: "Seasonal Celebrations",
                description: "Designs reflecting the beauty and mood of different seasons",
                icon: "🍂",
                keywords: ["seasonal colors", "weather patterns", "natural cycles"]
            },
            artistic: {
                name: "Artistic Styles",
                description: "Designs inspired by various art movements and aesthetic styles",
                icon: "🖼️",
                keywords: ["style-specific", "cultural", "historical", "movement-based"]
            }
        };
    }

    // Get random design idea
    getRandomIdea() {
        const themes = Object.keys(this.designIdeas);
        const randomTheme = themes[Math.floor(Math.random() * themes.length)];
        const themeIdeas = this.designIdeas[randomTheme];
        const randomIdea = themeIdeas[Math.floor(Math.random() * themeIdeas.length)];
        
        return {
            ...randomIdea,
            theme: randomTheme,
            colorCombination: this.getMatchingColorCombination(randomTheme)
        };
    }

    // Get ideas by theme
    getIdeasByTheme(theme) {
        return this.designIdeas[theme] || [];
    }

    // Get ideas by difficulty
    getIdeasByDifficulty(difficulty) {
        const allIdeas = [];
        Object.keys(this.designIdeas).forEach(theme => {
            this.designIdeas[theme].forEach(idea => {
                if (idea.difficulty === difficulty) {
                    allIdeas.push({...idea, theme});
                }
            });
        });
        return allIdeas;
    }

    // Get matching color combination for theme
    getMatchingColorCombination(theme) {
        const matchingCombos = this.colorCombinations.filter(combo => 
            combo.bestFor.includes(theme)
        );
        return matchingCombos[Math.floor(Math.random() * matchingCombos.length)];
    }

    // Generate random pattern combination
    generateRandomPatternCombo() {
        const patterns = ["spiral", "wave", "mandala", "geometric", "flower", "line", "curve", "circle"];
        const numPatterns = Math.floor(Math.random() * 3) + 1; // 1-3 patterns
        const selectedPatterns = [];
        
        for (let i = 0; i < numPatterns; i++) {
            const pattern = patterns[Math.floor(Math.random() * patterns.length)];
            if (!selectedPatterns.includes(pattern)) {
                selectedPatterns.push(pattern);
            }
        }
        
        const colorCombo = this.colorCombinations[Math.floor(Math.random() * this.colorCombinations.length)];
        
        return {
            patterns: selectedPatterns,
            colorCombination: colorCombo,
            title: `Random ${selectedPatterns.join(" + ")} Combination`,
            description: `Experimental combination of ${selectedPatterns.join(", ")} patterns with ${colorCombo.name} colors`
        };
    }

    // Get all themes
    getAllThemes() {
        return this.themes;
    }

    // Get all color combinations
    getAllColorCombinations() {
        return this.colorCombinations;
    }
}
