// AI API Integration for Embroidery Thread Art Design Generation

class AIAPIIntegration {
    constructor(canvas, patternGenerator, threadColors) {
        this.canvas = canvas;
        this.patternGenerator = patternGenerator;
        this.threadColors = threadColors;
        this.envConfig = envConfig; // Use global environment config
        this.apiKey = this.loadAPIKey();
        this.defaultPrompt = this.getDefaultPrompt();
    }

    loadAPIKey() {
        return this.envConfig.getGeminiAPIKey();
    }

    saveAPIKey(apiKey) {
        this.envConfig.saveGeminiAPIKey(apiKey);
        this.apiKey = apiKey;
    }

    isUsingEnvironmentAPIKey() {
        return this.envConfig.isUsingEnvironmentAPIKey();
    }

    getDefaultPrompt() {
        return `You are an expert embroidery thread art designer. Create a detailed design for an embroidery thread art piece.

CANVAS SPECIFICATIONS:
- Canvas dimensions: ${this.canvas.width}x${this.canvas.height} pixels
- Coordinate system: (0,0) is top-left, (${this.canvas.width},${this.canvas.height}) is bottom-right
- This is for physical embroidery thread art where colored threads are glued onto canvas and coated with epoxy

AVAILABLE THREAD COLORS (use only these hex codes):
${this.getAvailableColorsString()}

DESIGN REQUIREMENTS:
- Create a complete, full-canvas design that utilizes the entire space effectively
- Include multiple layers: background elements, main patterns, focus points, and details
- Ensure proper visual balance and composition
- Use only the provided thread colors
- Consider the physical constraints of thread art (threads have width, patterns should be achievable)

CUSTOM SHAPE CAPABILITIES:
You can create custom shapes beyond the predefined patterns using the "custom" type:
- "path": Array of x,y coordinates for connected lines
- "polygon": Array of points forming a closed shape
- "bezier": Control points for smooth curves (quadratic or cubic)
- "parametric": Mathematical equations (circle, ellipse, rose, lissajous, spiral, cardioid)
- "svg_path": SVG path strings for complex shapes
- "compound": Multiple shapes combined together
Use custom shapes to create unique, artistic patterns that still respect embroidery constraints.

RESPONSE FORMAT:
Return ONLY a valid JSON object with this exact structure:
{
  "design": {
    "title": "Design Name",
    "style": "style description",
    "backgroundElements": [
      {
        "type": "gradient|texture|pattern",
        "coordinates": {"x1": 0, "y1": 0, "x2": 800, "y2": 600},
        "colors": ["#hexcode1", "#hexcode2"],
        "opacity": 0.3
      }
    ],
    "mainPatterns": [
      {
        "type": "spiral|wave|mandala|geometric|flower|line|curve|circle|custom",
        "coordinates": {"centerX": 400, "centerY": 300},
        "parameters": {"radius": 100, "turns": 3, "sides": 6},
        "color": "#hexcode",
        "lineWidth": 3,
        "size": "small|medium|large",
        "customShape": {
          "shapeType": "path|polygon|bezier|compound|parametric|svg_path",
          "pathData": [{"x": 100, "y": 100}, {"x": 200, "y": 150}],
          "points": [{"x": 100, "y": 100}, {"x": 200, "y": 100}, {"x": 150, "y": 200}],
          "controlPoints": [{"x": 100, "y": 100}, {"x": 150, "y": 50}, {"x": 200, "y": 100}],
          "equation": "circle|ellipse|rose|lissajous|spiral|cardioid",
          "pathString": "M100,100 L200,100 Q250,150 200,200 Z"
        }
      }
    ],
    "focusPoints": [
      {
        "coordinates": {"x": 400, "y": 300},
        "type": "spiral|mandala|flower|geometric",
        "color": "#hexcode",
        "intensity": 1.0,
        "radius": 80
      }
    ],
    "detailElements": [
      {
        "type": "dots|lines|texture",
        "area": {"x": 100, "y": 100, "width": 200, "height": 150},
        "color": "#hexcode",
        "density": 0.5
      }
    ],
    "borders": [
      {
        "type": "simple|decorative|flowing",
        "margin": 20,
        "color": "#hexcode",
        "width": 3
      }
    ]
  },
  "metadata": {
    "complexity": "low|medium|high",
    "estimatedTime": "hours",
    "difficulty": "beginner|intermediate|advanced",
    "description": "Brief description of the design concept"
  }
}

Create a design based on the user's specifications:`;
    }

    getAvailableColorsString() {
        return this.threadColors.map(color => 
            `${color.name}: ${color.hex} (${color.brand})`
        ).join('\n');
    }

    async generateDesign(userPrompt, style, complexity, theme) {
        if (!this.apiKey) {
            throw new Error('Gemini API key not configured. Please set your API key in the settings.');
        }

        const fullPrompt = this.buildFullPrompt(userPrompt, style, complexity, theme);
        
        try {
            const response = await this.callGeminiAPI(fullPrompt);
            const designData = this.parseAIResponse(response);
            return designData;
        } catch (error) {
            console.error('AI API Error:', error);
            throw new Error(`AI generation failed: ${error.message}`);
        }
    }

    buildFullPrompt(userPrompt, style, complexity, theme) {
        const styleDescription = this.getStyleDescription(style);
        const complexityDescription = this.getComplexityDescription(complexity);
        const themeDescription = this.getThemeDescription(theme);

        return `${this.defaultPrompt}

STYLE: ${styleDescription}
COMPLEXITY: ${complexityDescription}
THEME: ${themeDescription}

USER REQUEST: ${userPrompt}

Generate a ${style} style embroidery thread art design with ${complexity} complexity focusing on ${theme} theme.`;
    }

    getStyleDescription(style) {
        const descriptions = {
            organic: "Flowing, natural patterns with curved lines and asymmetric balance",
            geometric: "Mathematical precision with straight lines, symmetric patterns, and angular forms",
            minimalist: "Simple forms with negative space, limited elements, and clean composition",
            bohemian: "Layered patterns with rich textures, eclectic mix, and decorative elements",
            artDeco: "Angular forms with metallic accents, symmetrical design, and stepped patterns"
        };
        return descriptions[style] || descriptions.organic;
    }

    getComplexityDescription(complexity) {
        const descriptions = {
            low: "Simple design with 2-3 main elements, minimal layers, easy to execute",
            medium: "Moderate complexity with 4-5 elements, balanced composition, intermediate skill",
            high: "Complex design with 6-8 elements, multiple layers, advanced techniques",
            very_high: "Highly intricate design with 8+ elements, maximum detail, expert level"
        };
        return descriptions[complexity] || descriptions.medium;
    }

    getThemeDescription(theme) {
        const descriptions = {
            nature: "Natural elements like trees, flowers, landscapes, organic forms",
            abstract: "Non-representational forms focusing on color, shape, and movement",
            seasonal: "Designs reflecting seasonal moods, colors, and natural cycles",
            artistic: "Inspired by art movements, cultural styles, and aesthetic traditions"
        };
        return descriptions[theme] || descriptions.abstract;
    }

    async callGeminiAPI(prompt) {
        const url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 2048,
            }
        };

        const response = await fetch(`${url}?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`API request failed: ${errorData.error?.message || response.statusText}`);
        }

        const data = await response.json();
        
        if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
            throw new Error('Invalid response format from Gemini API');
        }

        return data.candidates[0].content.parts[0].text;
    }

    parseAIResponse(responseText) {
        try {
            // Clean the response text to extract JSON
            let jsonText = responseText.trim();

            // Remove markdown code blocks if present
            if (jsonText.startsWith('```json')) {
                jsonText = jsonText.replace(/```json\s*/, '').replace(/\s*```$/, '');
            } else if (jsonText.startsWith('```')) {
                jsonText = jsonText.replace(/```\s*/, '').replace(/\s*```$/, '');
            }

            // Try to find JSON within the response if it's not pure JSON
            const jsonMatch = jsonText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                jsonText = jsonMatch[0];
            }

            // Parse the JSON
            const designData = JSON.parse(jsonText);

            // Validate the structure
            this.validateDesignData(designData);

            return designData;
        } catch (error) {
            console.error('Failed to parse AI response:', responseText);
            console.error('Parse error:', error);

            // Return a basic fallback structure if parsing fails completely
            return {
                design: {
                    title: "Fallback Design",
                    style: "organic",
                    backgroundElements: [],
                    mainPatterns: [{
                        type: "spiral",
                        coordinates: {centerX: this.canvas.width/2, centerY: this.canvas.height/2},
                        parameters: {radius: 80, turns: 3},
                        color: this.threadColors[0].hex,
                        lineWidth: 3,
                        size: "medium"
                    }],
                    focusPoints: [],
                    detailElements: [],
                    borders: []
                },
                metadata: {
                    complexity: "medium",
                    estimatedTime: "2-3 hours",
                    difficulty: "intermediate",
                    description: "Fallback design due to parsing error"
                }
            };
        }
    }

    validateDesignData(designData) {
        if (!designData.design) {
            throw new Error('Missing design object in AI response');
        }

        const design = designData.design;
        
        // Validate required arrays
        const requiredArrays = ['backgroundElements', 'mainPatterns', 'focusPoints', 'detailElements', 'borders'];
        requiredArrays.forEach(arrayName => {
            if (!Array.isArray(design[arrayName])) {
                design[arrayName] = [];
            }
        });

        // Validate coordinates are within canvas bounds
        this.validateCoordinates(design);
        
        // Validate colors are from available palette
        this.validateColors(design);
    }

    validateCoordinates(design) {
        const validatePoint = (point, name) => {
            if (!point || typeof point !== 'object') {
                console.warn(`${name} invalid point object, using default`);
                return { x: this.canvas.width / 2, y: this.canvas.height / 2 };
            }

            // Ensure x and y are numbers
            point.x = typeof point.x === 'number' ? point.x : this.canvas.width / 2;
            point.y = typeof point.y === 'number' ? point.y : this.canvas.height / 2;

            if (point.x < 0 || point.x > this.canvas.width ||
                point.y < 0 || point.y > this.canvas.height) {
                console.warn(`${name} coordinates out of bounds, adjusting:`, point);
                point.x = Math.max(0, Math.min(this.canvas.width, point.x));
                point.y = Math.max(0, Math.min(this.canvas.height, point.y));
            }
            return point;
        };

        // Validate main patterns
        design.mainPatterns.forEach((pattern, index) => {
            if (pattern.coordinates) {
                if (pattern.coordinates.centerX !== undefined) {
                    const validatedPoint = validatePoint({x: pattern.coordinates.centerX, y: pattern.coordinates.centerY}, `Pattern ${index}`);
                    pattern.coordinates.centerX = validatedPoint.x;
                    pattern.coordinates.centerY = validatedPoint.y;
                }
            }
        });

        // Validate focus points
        design.focusPoints.forEach((point, index) => {
            if (point.coordinates) {
                point.coordinates = validatePoint(point.coordinates, `Focus point ${index}`);
            }
        });
    }

    validateColors(design) {
        const availableColors = new Set(this.threadColors.map(c => c.hex.toUpperCase()));

        const validateColor = (color, context) => {
            if (!color || typeof color !== 'string') {
                console.warn(`Invalid color type in ${context}, using default`);
                return this.threadColors[0].hex;
            }
            if (!availableColors.has(color.toUpperCase())) {
                console.warn(`Invalid color ${color} in ${context}, using default`);
                return this.threadColors[0].hex; // Use first available color as default
            }
            return color;
        };

        // Validate colors in all design elements
        design.mainPatterns.forEach(pattern => {
            pattern.color = validateColor(pattern.color, 'main pattern');
        });

        design.focusPoints.forEach(point => {
            point.color = validateColor(point.color, 'focus point');
        });

        design.detailElements.forEach(element => {
            element.color = validateColor(element.color, 'detail element');
        });

        design.borders.forEach(border => {
            border.color = validateColor(border.color, 'border');
        });

        design.backgroundElements.forEach(bg => {
            if (bg.colors && Array.isArray(bg.colors)) {
                bg.colors = bg.colors.map(color => validateColor(color, 'background'));
            } else if (bg.colors) {
                // If colors is not an array, convert it or set default
                bg.colors = [validateColor(bg.colors, 'background')];
            }
        });
    }

    // Generate fallback design if AI fails
    generateFallbackDesign(style, complexity, theme) {
        const colors = this.getThemeColors(theme);
        
        return {
            design: {
                title: `Fallback ${style} Design`,
                style: style,
                backgroundElements: [{
                    type: "gradient",
                    coordinates: {x1: 0, y1: 0, x2: this.canvas.width, y2: this.canvas.height},
                    colors: [colors[0], colors[1]],
                    opacity: 0.2
                }],
                mainPatterns: [{
                    type: style === 'geometric' ? 'geometric' : 'spiral',
                    coordinates: {centerX: this.canvas.width/2, centerY: this.canvas.height/2},
                    parameters: {radius: 100, turns: 3, sides: 6},
                    color: colors[2],
                    lineWidth: 3,
                    size: "medium"
                }],
                focusPoints: [{
                    coordinates: {x: this.canvas.width/2, y: this.canvas.height/2},
                    type: "mandala",
                    color: colors[3],
                    intensity: 1.0,
                    radius: 80
                }],
                detailElements: [],
                borders: [{
                    type: "simple",
                    margin: 20,
                    color: colors[4],
                    width: 3
                }]
            },
            metadata: {
                complexity: complexity,
                estimatedTime: "2-4 hours",
                difficulty: "intermediate",
                description: "Fallback design generated when AI is unavailable"
            }
        };
    }

    getThemeColors(theme) {
        const themeColors = {
            nature: ['#228B22', '#50C878', '#8B4513', '#FFD700', '#87CEEB'],
            abstract: ['#663399', '#FF8C00', '#4169E1', '#DC143C', '#FFD700'],
            seasonal: ['#FF8C00', '#B22222', '#FFD700', '#32CD32', '#87CEEB'],
            artistic: ['#000000', '#FFFFFF', '#DC143C', '#FFD700', '#4169E1']
        };
        return themeColors[theme] || themeColors.abstract;
    }
}
