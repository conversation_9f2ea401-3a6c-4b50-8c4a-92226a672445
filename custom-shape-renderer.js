// Custom Shape Renderer for AI-Generated Embroidery Thread Art Patterns

class CustomShapeRenderer {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
    }

    // Main method to render custom shapes defined by AI
    renderCustomShape(shapeData, color, lineWidth) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';

        switch (shapeData.shapeType) {
            case 'path':
                this.renderPath(shapeData.pathData);
                break;
            case 'polygon':
                this.renderPolygon(shapeData.points);
                break;
            case 'bezier':
                this.renderBezierCurve(shapeData.controlPoints);
                break;
            case 'compound':
                this.renderCompoundShape(shapeData.shapes, color, lineWidth);
                break;
            case 'parametric':
                this.renderParametricShape(shapeData.equation, shapeData.parameters);
                break;
            case 'svg_path':
                this.renderSVGPath(shapeData.pathString);
                break;
            default:
                console.warn(`Unknown custom shape type: ${shapeData.shapeType}`);
                this.renderFallbackShape(shapeData);
        }
    }

    // Render path defined by coordinate arrays
    renderPath(pathData) {
        if (!pathData || !Array.isArray(pathData) || pathData.length === 0) return;

        this.ctx.beginPath();
        
        pathData.forEach((point, index) => {
            if (this.isValidPoint(point)) {
                if (index === 0) {
                    this.ctx.moveTo(point.x, point.y);
                } else {
                    this.ctx.lineTo(point.x, point.y);
                }
            }
        });
        
        this.ctx.stroke();
    }

    // Render polygon from points array
    renderPolygon(points) {
        if (!points || !Array.isArray(points) || points.length < 3) return;

        this.ctx.beginPath();
        
        points.forEach((point, index) => {
            if (this.isValidPoint(point)) {
                if (index === 0) {
                    this.ctx.moveTo(point.x, point.y);
                } else {
                    this.ctx.lineTo(point.x, point.y);
                }
            }
        });
        
        this.ctx.closePath();
        this.ctx.stroke();
    }

    // Render Bezier curves
    renderBezierCurve(controlPoints) {
        if (!controlPoints || !Array.isArray(controlPoints)) return;

        this.ctx.beginPath();
        
        if (controlPoints.length >= 4) {
            // Cubic Bezier curve
            const start = controlPoints[0];
            const cp1 = controlPoints[1];
            const cp2 = controlPoints[2];
            const end = controlPoints[3];
            
            if (this.isValidPoint(start) && this.isValidPoint(cp1) && 
                this.isValidPoint(cp2) && this.isValidPoint(end)) {
                this.ctx.moveTo(start.x, start.y);
                this.ctx.bezierCurveTo(cp1.x, cp1.y, cp2.x, cp2.y, end.x, end.y);
            }
        } else if (controlPoints.length >= 3) {
            // Quadratic Bezier curve
            const start = controlPoints[0];
            const cp = controlPoints[1];
            const end = controlPoints[2];
            
            if (this.isValidPoint(start) && this.isValidPoint(cp) && this.isValidPoint(end)) {
                this.ctx.moveTo(start.x, start.y);
                this.ctx.quadraticCurveTo(cp.x, cp.y, end.x, end.y);
            }
        }
        
        this.ctx.stroke();
    }

    // Render compound shapes (multiple shapes combined)
    renderCompoundShape(shapes, color, lineWidth) {
        if (!shapes || !Array.isArray(shapes)) return;

        shapes.forEach(shape => {
            if (shape.color) {
                this.ctx.strokeStyle = shape.color;
            }
            if (shape.lineWidth) {
                this.ctx.lineWidth = shape.lineWidth;
            }
            
            this.renderCustomShape(shape, shape.color || color, shape.lineWidth || lineWidth);
        });
    }

    // Render parametric shapes using mathematical equations
    renderParametricShape(equation, parameters) {
        if (!equation || !parameters) return;

        const { tMin = 0, tMax = 2 * Math.PI, steps = 100, centerX = 0, centerY = 0, scale = 1 } = parameters;
        
        this.ctx.beginPath();
        
        for (let i = 0; i <= steps; i++) {
            const t = tMin + (tMax - tMin) * (i / steps);
            const point = this.evaluateParametricEquation(equation, t, parameters);
            
            if (point) {
                const x = centerX + point.x * scale;
                const y = centerY + point.y * scale;
                
                if (this.isValidCoordinate(x, y)) {
                    if (i === 0) {
                        this.ctx.moveTo(x, y);
                    } else {
                        this.ctx.lineTo(x, y);
                    }
                }
            }
        }
        
        this.ctx.stroke();
    }

    // Evaluate parametric equations
    evaluateParametricEquation(equation, t, parameters) {
        try {
            // Support common parametric shapes
            switch (equation) {
                case 'circle':
                    return {
                        x: Math.cos(t),
                        y: Math.sin(t)
                    };
                case 'ellipse':
                    const { a = 1, b = 1 } = parameters;
                    return {
                        x: a * Math.cos(t),
                        y: b * Math.sin(t)
                    };
                case 'rose':
                    const { k = 3 } = parameters;
                    const r = Math.cos(k * t);
                    return {
                        x: r * Math.cos(t),
                        y: r * Math.sin(t)
                    };
                case 'lissajous':
                    const { A = 1, B = 1, a = 1, b = 1, delta = 0 } = parameters;
                    return {
                        x: A * Math.sin(a * t + delta),
                        y: B * Math.sin(b * t)
                    };
                case 'spiral':
                    const { growth = 0.1 } = parameters;
                    return {
                        x: t * growth * Math.cos(t),
                        y: t * growth * Math.sin(t)
                    };
                case 'cardioid':
                    const r_card = 1 - Math.cos(t);
                    return {
                        x: r_card * Math.cos(t),
                        y: r_card * Math.sin(t)
                    };
                default:
                    return null;
            }
        } catch (error) {
            console.warn('Error evaluating parametric equation:', error);
            return null;
        }
    }

    // Render SVG-like path strings
    renderSVGPath(pathString) {
        if (!pathString || typeof pathString !== 'string') return;

        try {
            const path = new Path2D(pathString);
            this.ctx.stroke(path);
        } catch (error) {
            console.warn('Error rendering SVG path:', error);
            // Fallback to manual parsing for basic SVG commands
            this.renderSVGPathManual(pathString);
        }
    }

    // Manual SVG path parsing for basic commands
    renderSVGPathManual(pathString) {
        const commands = this.parseSVGPath(pathString);
        
        this.ctx.beginPath();
        let currentX = 0, currentY = 0;
        
        commands.forEach(cmd => {
            switch (cmd.type) {
                case 'M': // Move to
                    currentX = cmd.x;
                    currentY = cmd.y;
                    this.ctx.moveTo(currentX, currentY);
                    break;
                case 'L': // Line to
                    currentX = cmd.x;
                    currentY = cmd.y;
                    this.ctx.lineTo(currentX, currentY);
                    break;
                case 'Q': // Quadratic curve to
                    this.ctx.quadraticCurveTo(cmd.x1, cmd.y1, cmd.x, cmd.y);
                    currentX = cmd.x;
                    currentY = cmd.y;
                    break;
                case 'C': // Cubic curve to
                    this.ctx.bezierCurveTo(cmd.x1, cmd.y1, cmd.x2, cmd.y2, cmd.x, cmd.y);
                    currentX = cmd.x;
                    currentY = cmd.y;
                    break;
                case 'Z': // Close path
                    this.ctx.closePath();
                    break;
            }
        });
        
        this.ctx.stroke();
    }

    // Basic SVG path parser
    parseSVGPath(pathString) {
        const commands = [];
        const regex = /([MLQCZ])\s*([^MLQCZ]*)/gi;
        let match;
        
        while ((match = regex.exec(pathString)) !== null) {
            const type = match[1].toUpperCase();
            const coords = match[2].trim().split(/[\s,]+/).map(Number).filter(n => !isNaN(n));
            
            switch (type) {
                case 'M':
                case 'L':
                    if (coords.length >= 2) {
                        commands.push({ type, x: coords[0], y: coords[1] });
                    }
                    break;
                case 'Q':
                    if (coords.length >= 4) {
                        commands.push({ type, x1: coords[0], y1: coords[1], x: coords[2], y: coords[3] });
                    }
                    break;
                case 'C':
                    if (coords.length >= 6) {
                        commands.push({ 
                            type, 
                            x1: coords[0], y1: coords[1], 
                            x2: coords[2], y2: coords[3], 
                            x: coords[4], y: coords[5] 
                        });
                    }
                    break;
                case 'Z':
                    commands.push({ type });
                    break;
            }
        }
        
        return commands;
    }

    // Fallback shape for unknown types
    renderFallbackShape(shapeData) {
        // Render a simple circle as fallback
        const centerX = shapeData.centerX || this.canvas.width / 2;
        const centerY = shapeData.centerY || this.canvas.height / 2;
        const radius = shapeData.radius || 50;
        
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        this.ctx.stroke();
    }

    // Utility methods
    isValidPoint(point) {
        return point && 
               typeof point.x === 'number' && 
               typeof point.y === 'number' && 
               this.isValidCoordinate(point.x, point.y);
    }

    isValidCoordinate(x, y) {
        return x >= -100 && x <= this.canvas.width + 100 && 
               y >= -100 && y <= this.canvas.height + 100;
    }

    // Method to validate custom shape data for embroidery constraints
    validateForEmbroidery(shapeData) {
        const warnings = [];
        
        // Check for overly complex paths
        if (shapeData.shapeType === 'path' && shapeData.pathData && shapeData.pathData.length > 200) {
            warnings.push('Path has many points - may be difficult to execute with thread');
        }
        
        // Check for very small details
        if (shapeData.shapeType === 'polygon' && shapeData.points) {
            const minDistance = this.getMinimumDistance(shapeData.points);
            if (minDistance < 5) {
                warnings.push('Shape has very small details - may not be visible with thread width');
            }
        }
        
        // Check for sharp angles
        if (shapeData.shapeType === 'polygon' && shapeData.points) {
            const sharpAngles = this.findSharpAngles(shapeData.points);
            if (sharpAngles.length > 0) {
                warnings.push('Shape has sharp angles - may be difficult to achieve with thread');
            }
        }
        
        return {
            isValid: warnings.length === 0,
            warnings: warnings
        };
    }

    getMinimumDistance(points) {
        let minDist = Infinity;
        for (let i = 0; i < points.length - 1; i++) {
            const dist = Math.sqrt(
                (points[i+1].x - points[i].x) ** 2 + 
                (points[i+1].y - points[i].y) ** 2
            );
            minDist = Math.min(minDist, dist);
        }
        return minDist;
    }

    findSharpAngles(points, threshold = 30) {
        const sharpAngles = [];
        
        for (let i = 1; i < points.length - 1; i++) {
            const angle = this.calculateAngle(points[i-1], points[i], points[i+1]);
            if (angle < threshold) {
                sharpAngles.push({ index: i, angle: angle });
            }
        }
        
        return sharpAngles;
    }

    calculateAngle(p1, p2, p3) {
        const v1 = { x: p1.x - p2.x, y: p1.y - p2.y };
        const v2 = { x: p3.x - p2.x, y: p3.y - p2.y };
        
        const dot = v1.x * v2.x + v1.y * v2.y;
        const mag1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
        const mag2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);
        
        const cosAngle = dot / (mag1 * mag2);
        const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI);
        
        return angle;
    }
}
