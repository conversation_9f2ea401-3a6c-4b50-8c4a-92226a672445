// Pre-defined patterns for embroidery thread art
class PatternGenerator {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
    }

    // Spiral pattern
    drawSpiral(centerX, centerY, maxRadius, turns, color, lineWidth) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        this.ctx.beginPath();
        
        const angleStep = 0.1;
        const radiusStep = maxRadius / (turns * 2 * Math.PI / angleStep);
        
        for (let angle = 0; angle <= turns * 2 * Math.PI; angle += angleStep) {
            const radius = angle * radiusStep;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);
            
            if (angle === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }
        
        this.ctx.stroke();
    }

    // Wave pattern
    drawWave(startX, startY, endX, endY, amplitude, frequency, color, lineWidth) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        this.ctx.beginPath();
        
        const distance = Math.sqrt((endX - startX) ** 2 + (endY - startY) ** 2);
        const steps = Math.floor(distance);
        
        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            const baseX = startX + t * (endX - startX);
            const baseY = startY + t * (endY - startY);
            
            const waveOffset = amplitude * Math.sin(t * frequency * 2 * Math.PI);
            const perpX = -(endY - startY) / distance;
            const perpY = (endX - startX) / distance;
            
            const x = baseX + waveOffset * perpX;
            const y = baseY + waveOffset * perpY;
            
            if (i === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }
        
        this.ctx.stroke();
    }

    // Mandala pattern
    drawMandala(centerX, centerY, radius, petals, color, lineWidth) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        
        const angleStep = (2 * Math.PI) / petals;
        
        // Outer circle
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        this.ctx.stroke();
        
        // Inner circles and petals
        for (let i = 0; i < petals; i++) {
            const angle = i * angleStep;
            const petalX = centerX + (radius * 0.7) * Math.cos(angle);
            const petalY = centerY + (radius * 0.7) * Math.sin(angle);
            
            // Petal circles
            this.ctx.beginPath();
            this.ctx.arc(petalX, petalY, radius * 0.3, 0, 2 * Math.PI);
            this.ctx.stroke();
            
            // Lines from center to petals
            this.ctx.beginPath();
            this.ctx.moveTo(centerX, centerY);
            this.ctx.lineTo(petalX, petalY);
            this.ctx.stroke();
        }
        
        // Center circle
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius * 0.2, 0, 2 * Math.PI);
        this.ctx.stroke();
    }

    // Geometric pattern
    drawGeometric(centerX, centerY, size, sides, layers, color, lineWidth) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        
        for (let layer = 1; layer <= layers; layer++) {
            const layerSize = (size * layer) / layers;
            this.drawPolygon(centerX, centerY, layerSize, sides);
        }
    }

    // Helper function to draw polygon
    drawPolygon(centerX, centerY, radius, sides) {
        this.ctx.beginPath();
        
        for (let i = 0; i <= sides; i++) {
            const angle = (i * 2 * Math.PI) / sides - Math.PI / 2;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);
            
            if (i === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }
        
        this.ctx.stroke();
    }

    // Celtic knot pattern
    drawCelticKnot(centerX, centerY, size, color, lineWidth) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        
        const points = [
            { x: centerX - size/2, y: centerY - size/2 },
            { x: centerX + size/2, y: centerY - size/2 },
            { x: centerX + size/2, y: centerY + size/2 },
            { x: centerX - size/2, y: centerY + size/2 }
        ];
        
        // Draw interwoven curves
        this.ctx.beginPath();
        this.ctx.moveTo(points[0].x, points[0].y);
        this.ctx.quadraticCurveTo(centerX, centerY - size/4, points[1].x, points[1].y);
        this.ctx.quadraticCurveTo(centerX + size/4, centerY, points[2].x, points[2].y);
        this.ctx.quadraticCurveTo(centerX, centerY + size/4, points[3].x, points[3].y);
        this.ctx.quadraticCurveTo(centerX - size/4, centerY, points[0].x, points[0].y);
        this.ctx.stroke();
    }

    // Flower pattern
    drawFlower(centerX, centerY, petalLength, petals, color, lineWidth) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        
        const angleStep = (2 * Math.PI) / petals;
        
        for (let i = 0; i < petals; i++) {
            const angle = i * angleStep;
            const endX = centerX + petalLength * Math.cos(angle);
            const endY = centerY + petalLength * Math.sin(angle);
            
            // Draw petal as a curved line
            this.ctx.beginPath();
            this.ctx.moveTo(centerX, centerY);
            
            const controlX = centerX + (petalLength * 0.6) * Math.cos(angle - 0.3);
            const controlY = centerY + (petalLength * 0.6) * Math.sin(angle - 0.3);
            
            this.ctx.quadraticCurveTo(controlX, controlY, endX, endY);
            
            const controlX2 = centerX + (petalLength * 0.6) * Math.cos(angle + 0.3);
            const controlY2 = centerY + (petalLength * 0.6) * Math.sin(angle + 0.3);
            
            this.ctx.quadraticCurveTo(controlX2, controlY2, centerX, centerY);
            this.ctx.stroke();
        }
        
        // Center circle
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, petalLength * 0.1, 0, 2 * Math.PI);
        this.ctx.stroke();
    }

    // Grid pattern
    drawGrid(spacing, color, lineWidth) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        
        // Vertical lines
        for (let x = 0; x <= this.canvas.width; x += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        // Horizontal lines
        for (let y = 0; y <= this.canvas.height; y += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }
}

// Pattern presets
const PATTERN_PRESETS = {
    spiral: {
        name: 'Spiral',
        description: 'Classic spiral pattern',
        params: { turns: 3, maxRadius: 100 }
    },
    wave: {
        name: 'Wave',
        description: 'Flowing wave pattern',
        params: { amplitude: 30, frequency: 2 }
    },
    mandala: {
        name: 'Mandala',
        description: 'Symmetrical mandala design',
        params: { petals: 8, radius: 120 }
    },
    geometric: {
        name: 'Geometric',
        description: 'Layered geometric shapes',
        params: { sides: 6, layers: 3, size: 100 }
    },
    flower: {
        name: 'Flower',
        description: 'Flower with petals',
        params: { petals: 6, petalLength: 80 }
    },
    celtic: {
        name: 'Celtic Knot',
        description: 'Interwoven Celtic design',
        params: { size: 120 }
    }
};
