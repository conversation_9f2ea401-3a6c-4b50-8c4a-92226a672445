// Embroidery thread color palette based on common thread colors
const THREAD_COLORS = [
    // Reds
    { name: 'Cherry Red', hex: '#DC143C', brand: 'D<PERSON> 817' },
    { name: '<PERSON>', hex: '#B22222', brand: 'DMC 902' },
    { name: '<PERSON>', hex: '#FF69B4', brand: 'DMC 3326' },
    { name: '<PERSON>', hex: '#FF7F50', brand: 'DMC 351' },
    
    // Blues
    { name: '<PERSON> Blue', hex: '#4169E1', brand: 'DMC 797' },
    { name: 'Navy Blue', hex: '#000080', brand: 'DMC 336' },
    { name: '<PERSON> Blue', hex: '#87CEEB', brand: 'DMC 519' },
    { name: 'Turquoise', hex: '#40E0D0', brand: 'DMC 3846' },
    
    // Greens
    { name: '<PERSON> Green', hex: '#228B22', brand: 'DMC 986' },
    { name: '<PERSON>', hex: '#50C878', brand: 'DMC 912' },
    { name: '<PERSON>e Green', hex: '#32CD32', brand: 'D<PERSON> 704' },
    { name: '<PERSON>', hex: '#9CAF88', brand: 'DMC 3013' },
    
    // Yellows/Oranges
    { name: 'Golden Yellow', hex: '#FFD700', brand: 'DMC 725' },
    { name: 'Sunshine', hex: '#FFF700', brand: 'DMC 307' },
    { name: 'Orange', hex: '#FF8C00', brand: 'DMC 970' },
    { name: 'Peach', hex: '#FFCBA4', brand: 'DMC 353' },
    
    // Purples
    { name: 'Royal Purple', hex: '#663399', brand: 'DMC 550' },
    { name: 'Lavender', hex: '#E6E6FA', brand: 'DMC 210' },
    { name: 'Violet', hex: '#8A2BE2', brand: 'DMC 552' },
    { name: 'Plum', hex: '#DDA0DD', brand: 'DMC 316' },
    
    // Neutrals
    { name: 'Black', hex: '#000000', brand: 'DMC 310' },
    { name: 'White', hex: '#FFFFFF', brand: 'DMC B5200' },
    { name: 'Silver', hex: '#C0C0C0', brand: 'DMC 415' },
    { name: 'Gold', hex: '#FFD700', brand: 'DMC E3852' },
    
    // Browns
    { name: 'Chocolate', hex: '#8B4513', brand: 'DMC 801' },
    { name: 'Tan', hex: '#D2B48C', brand: 'DMC 437' },
    { name: 'Coffee', hex: '#6F4E37', brand: 'DMC 938' },
    { name: 'Beige', hex: '#F5F5DC', brand: 'DMC 822' },
    
    // Pinks
    { name: 'Hot Pink', hex: '#FF1493', brand: 'DMC 718' },
    { name: 'Baby Pink', hex: '#FFB6C1', brand: 'DMC 818' },
    { name: 'Magenta', hex: '#FF00FF', brand: 'DMC 917' },
    { name: 'Dusty Rose', hex: '#BC8F8F', brand: 'DMC 3726' }
];

// Metallic thread colors
const METALLIC_COLORS = [
    { name: 'Gold Metallic', hex: '#FFD700', brand: 'DMC E3852' },
    { name: 'Silver Metallic', hex: '#C0C0C0', brand: 'DMC E168' },
    { name: 'Copper', hex: '#B87333', brand: 'DMC E301' },
    { name: 'Bronze', hex: '#CD7F32', brand: 'DMC E3031' }
];

// Function to get all colors
function getAllColors() {
    return [...THREAD_COLORS, ...METALLIC_COLORS];
}

// Function to get colors by category
function getColorsByCategory(category) {
    const categories = {
        'reds': THREAD_COLORS.slice(0, 4),
        'blues': THREAD_COLORS.slice(4, 8),
        'greens': THREAD_COLORS.slice(8, 12),
        'yellows': THREAD_COLORS.slice(12, 16),
        'purples': THREAD_COLORS.slice(16, 20),
        'neutrals': THREAD_COLORS.slice(20, 24),
        'browns': THREAD_COLORS.slice(24, 28),
        'pinks': THREAD_COLORS.slice(28, 32),
        'metallics': METALLIC_COLORS
    };
    
    return categories[category] || [];
}

// Function to find complementary colors
function getComplementaryColor(hexColor) {
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    
    // Calculate complementary RGB
    const compR = 255 - r;
    const compG = 255 - g;
    const compB = 255 - b;
    
    // Convert back to hex
    const compHex = '#' + 
        compR.toString(16).padStart(2, '0') +
        compG.toString(16).padStart(2, '0') +
        compB.toString(16).padStart(2, '0');
    
    return compHex;
}

// Function to suggest color combinations
function getColorCombinations() {
    return [
        {
            name: 'Sunset',
            colors: ['#FF8C00', '#FFD700', '#FF69B4', '#DC143C'],
            description: 'Warm sunset colors'
        },
        {
            name: 'Ocean',
            colors: ['#000080', '#4169E1', '#87CEEB', '#40E0D0'],
            description: 'Cool ocean blues'
        },
        {
            name: 'Forest',
            colors: ['#228B22', '#50C878', '#32CD32', '#8B4513'],
            description: 'Natural forest tones'
        },
        {
            name: 'Royal',
            colors: ['#663399', '#FFD700', '#000000', '#FFFFFF'],
            description: 'Elegant royal colors'
        },
        {
            name: 'Spring',
            colors: ['#32CD32', '#FFD700', '#FF69B4', '#87CEEB'],
            description: 'Fresh spring palette'
        },
        {
            name: 'Autumn',
            colors: ['#FF8C00', '#8B4513', '#FFD700', '#B22222'],
            description: 'Warm autumn colors'
        }
    ];
}
