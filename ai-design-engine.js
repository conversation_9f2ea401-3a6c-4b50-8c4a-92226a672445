// AI-Powered Design Generation Engine for Embroidery Thread Art

class AIDesignEngine {
    constructor(canvas, ctx, patternGenerator) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.patternGenerator = patternGenerator;
        this.designHistory = [];
        this.compositionRules = this.initializeCompositionRules();
        this.styleTransfers = this.initializeStyleTransfers();
    }

    initializeCompositionRules() {
        return {
            goldenRatio: 1.618,
            ruleOfThirds: {
                horizontalLines: [this.canvas.height / 3, (2 * this.canvas.height) / 3],
                verticalLines: [this.canvas.width / 3, (2 * this.canvas.width) / 3]
            },
            visualWeight: {
                center: { x: this.canvas.width / 2, y: this.canvas.height / 2, weight: 1.0 },
                corners: [
                    { x: this.canvas.width * 0.1, y: this.canvas.height * 0.1, weight: 0.3 },
                    { x: this.canvas.width * 0.9, y: this.canvas.height * 0.1, weight: 0.3 },
                    { x: this.canvas.width * 0.1, y: this.canvas.height * 0.9, weight: 0.3 },
                    { x: this.canvas.width * 0.9, y: this.canvas.height * 0.9, weight: 0.3 }
                ]
            },
            margins: {
                top: this.canvas.height * 0.08,
                bottom: this.canvas.height * 0.08,
                left: this.canvas.width * 0.08,
                right: this.canvas.width * 0.08
            }
        };
    }

    initializeStyleTransfers() {
        return {
            organic: {
                name: "Organic Flow",
                characteristics: ["curved_lines", "natural_patterns", "asymmetric_balance"],
                colorHarmony: "analogous",
                complexity: "medium"
            },
            geometric: {
                name: "Geometric Precision",
                characteristics: ["straight_lines", "symmetric_patterns", "mathematical_ratios"],
                colorHarmony: "complementary",
                complexity: "high"
            },
            minimalist: {
                name: "Minimalist Zen",
                characteristics: ["simple_forms", "negative_space", "limited_elements"],
                colorHarmony: "monochromatic",
                complexity: "low"
            },
            bohemian: {
                name: "Bohemian Tapestry",
                characteristics: ["layered_patterns", "rich_textures", "eclectic_mix"],
                colorHarmony: "triadic",
                complexity: "very_high"
            },
            artDeco: {
                name: "Art Deco Elegance",
                characteristics: ["angular_forms", "metallic_accents", "symmetrical_design"],
                colorHarmony: "split_complementary",
                complexity: "high"
            }
        };
    }

    // Main AI Design Generation Method
    generateCompleteDesign(style, complexity, theme, colorPalette) {
        this.saveCurrentState();
        this.clearCanvas();
        
        const designSchema = this.createDesignSchema(style, complexity, theme, colorPalette);
        const composition = this.analyzeAndOptimizeComposition(designSchema);
        
        this.renderFullCanvasDesign(composition);
        
        return {
            schema: designSchema,
            composition: composition,
            metadata: this.generateDesignMetadata(designSchema, composition)
        };
    }

    createDesignSchema(style, complexity, theme, colorPalette) {
        const schema = {
            style: style,
            complexity: complexity,
            theme: theme,
            colorPalette: colorPalette,
            layers: [],
            focusPoints: [],
            backgroundElements: [],
            borders: [],
            textureAreas: []
        };

        // Generate background layer
        schema.layers.push(this.generateBackgroundLayer(style, theme, colorPalette));
        
        // Generate main composition layers based on complexity
        const numLayers = this.getLayerCount(complexity);
        for (let i = 0; i < numLayers; i++) {
            schema.layers.push(this.generateCompositionLayer(style, theme, colorPalette, i, numLayers));
        }

        // Generate focus points using rule of thirds and golden ratio
        schema.focusPoints = this.generateFocusPoints(style, complexity);
        
        // Generate border elements
        schema.borders = this.generateBorderElements(style, theme, colorPalette);
        
        // Generate texture areas for visual interest
        schema.textureAreas = this.generateTextureAreas(style, complexity);

        return schema;
    }

    generateBackgroundLayer(style, theme, colorPalette) {
        const backgroundTypes = {
            organic: ['flowing_gradient', 'organic_texture', 'subtle_waves'],
            geometric: ['grid_pattern', 'angular_shapes', 'mathematical_tessellation'],
            minimalist: ['solid_color', 'subtle_gradient', 'minimal_texture'],
            bohemian: ['rich_texture', 'layered_patterns', 'complex_background'],
            artDeco: ['angular_gradient', 'geometric_texture', 'metallic_base']
        };

        const bgType = this.selectRandomElement(backgroundTypes[style] || backgroundTypes.organic);
        
        return {
            type: 'background',
            pattern: bgType,
            colors: colorPalette.slice(0, 2), // Use first 2 colors for background
            coverage: 'full_canvas',
            opacity: 0.3,
            blendMode: 'multiply'
        };
    }

    generateCompositionLayer(style, theme, colorPalette, layerIndex, totalLayers) {
        const patterns = this.getStylePatterns(style);
        const selectedPattern = this.selectRandomElement(patterns);
        
        // Calculate layer properties based on hierarchy
        const isMainLayer = layerIndex === Math.floor(totalLayers / 2);
        const size = isMainLayer ? 'large' : (layerIndex < totalLayers / 2 ? 'medium' : 'small');
        
        return {
            type: 'composition',
            pattern: selectedPattern,
            colors: this.selectLayerColors(colorPalette, layerIndex, totalLayers),
            size: size,
            position: this.calculateOptimalPosition(layerIndex, totalLayers, style),
            weight: isMainLayer ? 1.0 : 0.6 - (layerIndex * 0.1),
            blendMode: 'normal'
        };
    }

    generateFocusPoints(style, complexity) {
        const focusPoints = [];
        const numFocusPoints = complexity === 'low' ? 1 : complexity === 'medium' ? 2 : 3;
        
        // Use rule of thirds intersections as primary focus points
        const intersections = [
            { x: this.canvas.width / 3, y: this.canvas.height / 3 },
            { x: (2 * this.canvas.width) / 3, y: this.canvas.height / 3 },
            { x: this.canvas.width / 3, y: (2 * this.canvas.height) / 3 },
            { x: (2 * this.canvas.width) / 3, y: (2 * this.canvas.height) / 3 }
        ];

        for (let i = 0; i < numFocusPoints; i++) {
            const intersection = intersections[i % intersections.length];
            focusPoints.push({
                x: intersection.x + (Math.random() - 0.5) * 50, // Add slight randomness
                y: intersection.y + (Math.random() - 0.5) * 50,
                intensity: 1.0 - (i * 0.2),
                radius: 80 - (i * 20),
                type: this.selectFocusPointType(style)
            });
        }

        return focusPoints;
    }

    generateBorderElements(style, theme, colorPalette) {
        const borderStyles = {
            organic: ['flowing_vine', 'organic_frame', 'natural_border'],
            geometric: ['angular_frame', 'geometric_border', 'mathematical_edge'],
            minimalist: ['simple_line', 'minimal_frame', 'subtle_border'],
            bohemian: ['decorative_border', 'ornate_frame', 'textured_edge'],
            artDeco: ['angular_deco', 'metallic_frame', 'stepped_border']
        };

        const borderType = this.selectRandomElement(borderStyles[style] || borderStyles.organic);
        
        return [{
            type: 'border',
            pattern: borderType,
            colors: [colorPalette[colorPalette.length - 1]], // Use last color for borders
            width: this.canvas.width * 0.05,
            style: style,
            corners: 'rounded'
        }];
    }

    generateTextureAreas(style, complexity) {
        if (complexity === 'low') return [];
        
        const numTextureAreas = complexity === 'medium' ? 2 : 4;
        const textureAreas = [];
        
        for (let i = 0; i < numTextureAreas; i++) {
            textureAreas.push({
                type: 'texture',
                pattern: this.selectTexturePattern(style),
                x: Math.random() * this.canvas.width * 0.6 + this.canvas.width * 0.2,
                y: Math.random() * this.canvas.height * 0.6 + this.canvas.height * 0.2,
                width: this.canvas.width * (0.1 + Math.random() * 0.2),
                height: this.canvas.height * (0.1 + Math.random() * 0.2),
                opacity: 0.4,
                density: complexity === 'medium' ? 0.5 : 0.8
            });
        }
        
        return textureAreas;
    }

    analyzeAndOptimizeComposition(designSchema) {
        // Analyze visual weight distribution
        const weightMap = this.calculateVisualWeightMap(designSchema);
        
        // Optimize element positions for better balance
        const optimizedSchema = this.optimizeElementPositions(designSchema, weightMap);
        
        // Ensure proper visual hierarchy
        const hierarchyOptimized = this.optimizeVisualHierarchy(optimizedSchema);
        
        // Calculate flow and movement paths
        const flowPaths = this.calculateVisualFlow(hierarchyOptimized);
        
        return {
            schema: hierarchyOptimized,
            weightMap: weightMap,
            flowPaths: flowPaths,
            balance: this.calculateCompositionBalance(hierarchyOptimized),
            harmony: this.calculateColorHarmony(hierarchyOptimized.colorPalette)
        };
    }

    renderFullCanvasDesign(composition) {
        const schema = composition.schema;
        
        // Render layers in order (background first)
        schema.layers.forEach((layer, index) => {
            this.renderLayer(layer, composition);
        });
        
        // Render focus points
        schema.focusPoints.forEach(focusPoint => {
            this.renderFocusPoint(focusPoint, schema.colorPalette);
        });
        
        // Render borders
        schema.borders.forEach(border => {
            this.renderBorder(border);
        });
        
        // Render texture areas
        schema.textureAreas.forEach(textureArea => {
            this.renderTextureArea(textureArea, schema.colorPalette);
        });
        
        // Apply visual flow enhancements
        this.enhanceVisualFlow(composition.flowPaths, schema.colorPalette);
    }

    // Helper methods
    getLayerCount(complexity) {
        switch (complexity) {
            case 'low': return 2;
            case 'medium': return 4;
            case 'high': return 6;
            case 'very_high': return 8;
            default: return 3;
        }
    }

    getStylePatterns(style) {
        const stylePatterns = {
            organic: ['spiral', 'wave', 'flower', 'organic_flow'],
            geometric: ['geometric', 'mandala', 'tessellation', 'angular'],
            minimalist: ['circle', 'line', 'simple_geometric'],
            bohemian: ['mandala', 'spiral', 'flower', 'decorative'],
            artDeco: ['geometric', 'angular', 'stepped', 'metallic']
        };
        
        return stylePatterns[style] || stylePatterns.organic;
    }

    selectRandomElement(array) {
        return array[Math.floor(Math.random() * array.length)];
    }

    selectLayerColors(colorPalette, layerIndex, totalLayers) {
        const colorsPerLayer = Math.max(1, Math.floor(colorPalette.length / totalLayers));
        const startIndex = layerIndex * colorsPerLayer;
        return colorPalette.slice(startIndex, startIndex + colorsPerLayer + 1);
    }

    calculateOptimalPosition(layerIndex, totalLayers, style) {
        // Use golden ratio and rule of thirds for positioning
        const goldenX = this.canvas.width / this.compositionRules.goldenRatio;
        const goldenY = this.canvas.height / this.compositionRules.goldenRatio;
        
        const positions = [
            { x: goldenX, y: goldenY },
            { x: this.canvas.width - goldenX, y: goldenY },
            { x: goldenX, y: this.canvas.height - goldenY },
            { x: this.canvas.width - goldenX, y: this.canvas.height - goldenY },
            { x: this.canvas.width / 2, y: goldenY },
            { x: this.canvas.width / 2, y: this.canvas.height - goldenY }
        ];
        
        return positions[layerIndex % positions.length];
    }

    selectFocusPointType(style) {
        const focusTypes = {
            organic: ['spiral_focus', 'flower_center', 'organic_burst'],
            geometric: ['geometric_center', 'angular_focus', 'mathematical_point'],
            minimalist: ['simple_circle', 'minimal_point', 'subtle_focus'],
            bohemian: ['decorative_center', 'ornate_focus', 'rich_detail'],
            artDeco: ['angular_center', 'metallic_focus', 'stepped_point']
        };
        
        const types = focusTypes[style] || focusTypes.organic;
        return this.selectRandomElement(types);
    }

    selectTexturePattern(style) {
        const texturePatterns = {
            organic: ['organic_dots', 'flowing_lines', 'natural_texture'],
            geometric: ['grid_texture', 'angular_pattern', 'mathematical_dots'],
            minimalist: ['subtle_dots', 'minimal_lines', 'simple_texture'],
            bohemian: ['rich_texture', 'complex_pattern', 'layered_dots'],
            artDeco: ['angular_texture', 'metallic_pattern', 'stepped_lines']
        };
        
        const patterns = texturePatterns[style] || texturePatterns.organic;
        return this.selectRandomElement(patterns);
    }

    saveCurrentState() {
        this.designHistory.push(this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height));
    }

    clearCanvas() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    // Rendering Methods
    renderLayer(layer, composition) {
        const originalAlpha = this.ctx.globalAlpha;
        this.ctx.globalAlpha = layer.opacity || 1.0;

        switch (layer.type) {
            case 'background':
                this.renderBackgroundLayer(layer);
                break;
            case 'composition':
                this.renderCompositionLayer(layer, composition);
                break;
        }

        this.ctx.globalAlpha = originalAlpha;
    }

    renderBackgroundLayer(layer) {
        const colors = layer.colors;

        switch (layer.pattern) {
            case 'flowing_gradient':
                this.createFlowingGradient(colors);
                break;
            case 'organic_texture':
                this.createOrganicTexture(colors);
                break;
            case 'subtle_waves':
                this.createSubtleWaves(colors);
                break;
            case 'grid_pattern':
                this.createGridPattern(colors);
                break;
            case 'angular_shapes':
                this.createAngularShapes(colors);
                break;
            case 'solid_color':
                this.createSolidBackground(colors[0]);
                break;
            default:
                this.createSolidBackground(colors[0]);
        }
    }

    renderCompositionLayer(layer, composition) {
        const position = layer.position;
        const colors = layer.colors;
        const size = this.getSizeMultiplier(layer.size);

        switch (layer.pattern) {
            case 'spiral':
                this.patternGenerator.drawSpiral(
                    position.x, position.y,
                    60 * size, 2.5,
                    colors[0], 2
                );
                break;
            case 'wave':
                this.renderWavePattern(position, colors, size);
                break;
            case 'flower':
                this.patternGenerator.drawFlower(
                    position.x, position.y,
                    50 * size, 8,
                    colors[0], 2
                );
                break;
            case 'geometric':
                this.patternGenerator.drawGeometric(
                    position.x, position.y,
                    50 * size, 6, 3,
                    colors[0], 2
                );
                break;
            case 'mandala':
                this.patternGenerator.drawMandala(
                    position.x, position.y,
                    60 * size, 8,
                    colors[0], 2
                );
                break;
            case 'organic_flow':
                this.renderOrganicFlow(position, colors, size);
                break;
            case 'tessellation':
                this.renderTessellation(position, colors, size);
                break;
            case 'angular':
                this.renderAngularPattern(position, colors, size);
                break;
        }
    }

    renderFocusPoint(focusPoint, colorPalette) {
        const color = colorPalette[0];

        switch (focusPoint.type) {
            case 'spiral_focus':
                this.patternGenerator.drawSpiral(
                    focusPoint.x, focusPoint.y,
                    focusPoint.radius, 3,
                    color, 3
                );
                break;
            case 'flower_center':
                this.patternGenerator.drawFlower(
                    focusPoint.x, focusPoint.y,
                    focusPoint.radius, 12,
                    color, 3
                );
                break;
            case 'geometric_center':
                this.patternGenerator.drawGeometric(
                    focusPoint.x, focusPoint.y,
                    focusPoint.radius, 8, 4,
                    color, 3
                );
                break;
            case 'simple_circle':
                this.ctx.strokeStyle = color;
                this.ctx.lineWidth = 3;
                this.ctx.beginPath();
                this.ctx.arc(focusPoint.x, focusPoint.y, focusPoint.radius, 0, 2 * Math.PI);
                this.ctx.stroke();
                break;
            default:
                this.renderDefaultFocusPoint(focusPoint, color);
        }
    }

    renderBorder(border) {
        const margin = 20;
        const color = border.colors[0];

        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = border.width;

        switch (border.pattern) {
            case 'flowing_vine':
                this.renderFlowingVineBorder(margin, color);
                break;
            case 'geometric_border':
                this.renderGeometricBorder(margin, color);
                break;
            case 'simple_line':
                this.renderSimpleBorder(margin, color);
                break;
            case 'decorative_border':
                this.renderDecorativeBorder(margin, color);
                break;
            default:
                this.renderSimpleBorder(margin, color);
        }
    }

    renderTextureArea(textureArea, colorPalette) {
        const originalAlpha = this.ctx.globalAlpha;
        this.ctx.globalAlpha = textureArea.opacity;

        const color = colorPalette[Math.floor(Math.random() * colorPalette.length)];

        switch (textureArea.pattern) {
            case 'organic_dots':
                this.renderOrganicDots(textureArea, color);
                break;
            case 'flowing_lines':
                this.renderFlowingLines(textureArea, color);
                break;
            case 'grid_texture':
                this.renderGridTexture(textureArea, color);
                break;
            case 'subtle_dots':
                this.renderSubtleDots(textureArea, color);
                break;
            default:
                this.renderOrganicDots(textureArea, color);
        }

        this.ctx.globalAlpha = originalAlpha;
    }

    // Background rendering methods
    createFlowingGradient(colors) {
        const gradient = this.ctx.createLinearGradient(0, 0, this.canvas.width, this.canvas.height);
        colors.forEach((color, index) => {
            gradient.addColorStop(index / (colors.length - 1), color);
        });

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    createOrganicTexture(colors) {
        const color = colors[0];
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 1;

        // Create organic flowing lines across the canvas
        for (let i = 0; i < 20; i++) {
            this.ctx.beginPath();
            const startX = Math.random() * this.canvas.width;
            const startY = Math.random() * this.canvas.height;

            let currentX = startX;
            let currentY = startY;

            this.ctx.moveTo(currentX, currentY);

            for (let j = 0; j < 50; j++) {
                currentX += (Math.random() - 0.5) * 20;
                currentY += (Math.random() - 0.5) * 20;

                // Keep within canvas bounds
                currentX = Math.max(0, Math.min(this.canvas.width, currentX));
                currentY = Math.max(0, Math.min(this.canvas.height, currentY));

                this.ctx.lineTo(currentX, currentY);
            }

            this.ctx.stroke();
        }
    }

    createSubtleWaves(colors) {
        const color = colors[0];

        for (let y = 0; y < this.canvas.height; y += 40) {
            this.patternGenerator.drawWave(
                0, y,
                this.canvas.width, y,
                10, 3,
                color, 1
            );
        }
    }

    createGridPattern(colors) {
        const color = colors[0];
        const spacing = 30;

        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 0.5;

        // Vertical lines
        for (let x = spacing; x < this.canvas.width; x += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        // Horizontal lines
        for (let y = spacing; y < this.canvas.height; y += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    createAngularShapes(colors) {
        const color = colors[0];
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;

        // Create random angular shapes across the canvas
        for (let i = 0; i < 15; i++) {
            const centerX = Math.random() * this.canvas.width;
            const centerY = Math.random() * this.canvas.height;
            const size = 20 + Math.random() * 40;
            const sides = 3 + Math.floor(Math.random() * 5);

            this.ctx.beginPath();
            for (let j = 0; j <= sides; j++) {
                const angle = (j * 2 * Math.PI) / sides;
                const x = centerX + size * Math.cos(angle);
                const y = centerY + size * Math.sin(angle);

                if (j === 0) {
                    this.ctx.moveTo(x, y);
                } else {
                    this.ctx.lineTo(x, y);
                }
            }
            this.ctx.stroke();
        }
    }

    createSolidBackground(color) {
        this.ctx.fillStyle = color;
        this.ctx.globalAlpha = 0.1;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.globalAlpha = 1.0;
    }

    // Additional rendering methods
    getSizeMultiplier(size) {
        switch (size) {
            case 'small': return 0.5;
            case 'medium': return 1.0;
            case 'large': return 1.5;
            default: return 1.0;
        }
    }

    renderWavePattern(position, colors, size) {
        const waveLength = 100 * size;
        this.patternGenerator.drawWave(
            position.x - waveLength/2, position.y,
            position.x + waveLength/2, position.y,
            20 * size, 2,
            colors[0], 2
        );
    }

    renderOrganicFlow(position, colors, size) {
        const color = colors[0];
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;

        // Create flowing organic lines
        for (let i = 0; i < 5; i++) {
            this.ctx.beginPath();
            const startAngle = (i * 2 * Math.PI) / 5;
            const startX = position.x + 30 * size * Math.cos(startAngle);
            const startY = position.y + 30 * size * Math.sin(startAngle);

            this.ctx.moveTo(startX, startY);

            let currentX = startX;
            let currentY = startY;

            for (let j = 0; j < 20; j++) {
                const flowAngle = startAngle + (j * 0.2);
                currentX += 5 * Math.cos(flowAngle) + (Math.random() - 0.5) * 10;
                currentY += 5 * Math.sin(flowAngle) + (Math.random() - 0.5) * 10;
                this.ctx.lineTo(currentX, currentY);
            }

            this.ctx.stroke();
        }
    }

    renderTessellation(position, colors, size) {
        const color = colors[0];
        const hexSize = 15 * size;

        // Create hexagonal tessellation pattern
        for (let row = -2; row <= 2; row++) {
            for (let col = -2; col <= 2; col++) {
                const x = position.x + col * hexSize * 1.5;
                const y = position.y + row * hexSize * Math.sqrt(3) + (col % 2) * hexSize * Math.sqrt(3) / 2;

                this.drawHexagon(x, y, hexSize, color);
            }
        }
    }

    renderAngularPattern(position, colors, size) {
        const color = colors[0];
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;

        // Create angular geometric pattern
        const numLines = 8;
        for (let i = 0; i < numLines; i++) {
            const angle = (i * 2 * Math.PI) / numLines;
            const length = 40 * size;

            this.ctx.beginPath();
            this.ctx.moveTo(position.x, position.y);
            this.ctx.lineTo(
                position.x + length * Math.cos(angle),
                position.y + length * Math.sin(angle)
            );
            this.ctx.stroke();

            // Add angular decorations
            const midX = position.x + (length/2) * Math.cos(angle);
            const midY = position.y + (length/2) * Math.sin(angle);

            this.ctx.beginPath();
            this.ctx.moveTo(midX - 10, midY - 10);
            this.ctx.lineTo(midX + 10, midY - 10);
            this.ctx.lineTo(midX, midY + 10);
            this.ctx.closePath();
            this.ctx.stroke();
        }
    }

    drawHexagon(centerX, centerY, size, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();

        for (let i = 0; i < 6; i++) {
            const angle = (i * Math.PI) / 3;
            const x = centerX + size * Math.cos(angle);
            const y = centerY + size * Math.sin(angle);

            if (i === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }

        this.ctx.closePath();
        this.ctx.stroke();
    }

    renderDefaultFocusPoint(focusPoint, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 3;

        // Draw concentric circles
        for (let i = 1; i <= 3; i++) {
            this.ctx.beginPath();
            this.ctx.arc(focusPoint.x, focusPoint.y, focusPoint.radius * i / 3, 0, 2 * Math.PI);
            this.ctx.stroke();
        }
    }

    // Border rendering methods
    renderFlowingVineBorder(margin, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;

        // Top border
        this.patternGenerator.drawWave(
            margin, margin,
            this.canvas.width - margin, margin,
            10, 5, color, 2
        );

        // Bottom border
        this.patternGenerator.drawWave(
            margin, this.canvas.height - margin,
            this.canvas.width - margin, this.canvas.height - margin,
            10, 5, color, 2
        );

        // Left border
        this.patternGenerator.drawWave(
            margin, margin,
            margin, this.canvas.height - margin,
            10, 5, color, 2
        );

        // Right border
        this.patternGenerator.drawWave(
            this.canvas.width - margin, margin,
            this.canvas.width - margin, this.canvas.height - margin,
            10, 5, color, 2
        );
    }

    renderGeometricBorder(margin, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;

        const cornerSize = 20;

        // Draw geometric corner elements
        const corners = [
            { x: margin, y: margin },
            { x: this.canvas.width - margin, y: margin },
            { x: this.canvas.width - margin, y: this.canvas.height - margin },
            { x: margin, y: this.canvas.height - margin }
        ];

        corners.forEach(corner => {
            this.patternGenerator.drawGeometric(corner.x, corner.y, cornerSize, 4, 2, color, 2);
        });

        // Connect corners with lines
        this.ctx.beginPath();
        this.ctx.rect(margin, margin, this.canvas.width - 2*margin, this.canvas.height - 2*margin);
        this.ctx.stroke();
    }

    renderSimpleBorder(margin, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.rect(margin, margin, this.canvas.width - 2*margin, this.canvas.height - 2*margin);
        this.ctx.stroke();
    }

    renderDecorativeBorder(margin, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;

        // Create decorative pattern around border
        const spacing = 30;

        // Top and bottom
        for (let x = margin + spacing; x < this.canvas.width - margin; x += spacing) {
            this.drawDecorativeElement(x, margin, color);
            this.drawDecorativeElement(x, this.canvas.height - margin, color);
        }

        // Left and right
        for (let y = margin + spacing; y < this.canvas.height - margin; y += spacing) {
            this.drawDecorativeElement(margin, y, color);
            this.drawDecorativeElement(this.canvas.width - margin, y, color);
        }
    }

    drawDecorativeElement(x, y, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 1;

        // Draw small decorative diamond
        this.ctx.beginPath();
        this.ctx.moveTo(x, y - 5);
        this.ctx.lineTo(x + 5, y);
        this.ctx.lineTo(x, y + 5);
        this.ctx.lineTo(x - 5, y);
        this.ctx.closePath();
        this.ctx.stroke();
    }

    // Texture rendering methods
    renderOrganicDots(textureArea, color) {
        this.ctx.fillStyle = color;

        const numDots = Math.floor(textureArea.density * 50);

        for (let i = 0; i < numDots; i++) {
            const x = textureArea.x + Math.random() * textureArea.width;
            const y = textureArea.y + Math.random() * textureArea.height;
            const radius = 1 + Math.random() * 3;

            this.ctx.beginPath();
            this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }

    renderFlowingLines(textureArea, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 1;

        const numLines = Math.floor(textureArea.density * 10);

        for (let i = 0; i < numLines; i++) {
            this.ctx.beginPath();
            const startX = textureArea.x + Math.random() * textureArea.width;
            const startY = textureArea.y + Math.random() * textureArea.height;

            this.ctx.moveTo(startX, startY);

            let currentX = startX;
            let currentY = startY;

            for (let j = 0; j < 10; j++) {
                currentX += (Math.random() - 0.5) * 10;
                currentY += (Math.random() - 0.5) * 10;

                // Keep within texture area
                currentX = Math.max(textureArea.x, Math.min(textureArea.x + textureArea.width, currentX));
                currentY = Math.max(textureArea.y, Math.min(textureArea.y + textureArea.height, currentY));

                this.ctx.lineTo(currentX, currentY);
            }

            this.ctx.stroke();
        }
    }

    renderGridTexture(textureArea, color) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 0.5;

        const spacing = 10 / textureArea.density;

        // Vertical lines
        for (let x = textureArea.x; x <= textureArea.x + textureArea.width; x += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, textureArea.y);
            this.ctx.lineTo(x, textureArea.y + textureArea.height);
            this.ctx.stroke();
        }

        // Horizontal lines
        for (let y = textureArea.y; y <= textureArea.y + textureArea.height; y += spacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(textureArea.x, y);
            this.ctx.lineTo(textureArea.x + textureArea.width, y);
            this.ctx.stroke();
        }
    }

    renderSubtleDots(textureArea, color) {
        this.ctx.fillStyle = color;

        const numDots = Math.floor(textureArea.density * 20);

        for (let i = 0; i < numDots; i++) {
            const x = textureArea.x + Math.random() * textureArea.width;
            const y = textureArea.y + Math.random() * textureArea.height;
            const radius = 0.5 + Math.random() * 1;

            this.ctx.beginPath();
            this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }

    // Composition analysis methods
    calculateVisualWeightMap(designSchema) {
        const weightMap = [];
        const gridSize = 20;

        for (let x = 0; x < this.canvas.width; x += gridSize) {
            weightMap[x] = [];
            for (let y = 0; y < this.canvas.height; y += gridSize) {
                weightMap[x][y] = this.calculatePointWeight(x, y, designSchema);
            }
        }

        return weightMap;
    }

    calculatePointWeight(x, y, designSchema) {
        let weight = 0;

        // Add weight from focus points
        designSchema.focusPoints.forEach(focusPoint => {
            const distance = Math.sqrt((x - focusPoint.x) ** 2 + (y - focusPoint.y) ** 2);
            const influence = Math.max(0, focusPoint.radius - distance) / focusPoint.radius;
            weight += influence * focusPoint.intensity;
        });

        // Add weight from composition layers
        designSchema.layers.forEach(layer => {
            if (layer.position) {
                const distance = Math.sqrt((x - layer.position.x) ** 2 + (y - layer.position.y) ** 2);
                const influence = Math.max(0, 100 - distance) / 100;
                weight += influence * (layer.weight || 0.5);
            }
        });

        return Math.min(1, weight);
    }

    optimizeElementPositions(designSchema, weightMap) {
        // Adjust positions to improve balance
        const optimizedSchema = JSON.parse(JSON.stringify(designSchema));

        // Calculate current center of mass
        const centerOfMass = this.calculateCenterOfMass(weightMap);
        const canvasCenter = { x: this.canvas.width / 2, y: this.canvas.height / 2 };

        // If center of mass is too far from canvas center, adjust positions
        const offset = {
            x: (canvasCenter.x - centerOfMass.x) * 0.3,
            y: (canvasCenter.y - centerOfMass.y) * 0.3
        };

        optimizedSchema.layers.forEach(layer => {
            if (layer.position) {
                layer.position.x += offset.x;
                layer.position.y += offset.y;

                // Keep within canvas bounds
                layer.position.x = Math.max(50, Math.min(this.canvas.width - 50, layer.position.x));
                layer.position.y = Math.max(50, Math.min(this.canvas.height - 50, layer.position.y));
            }
        });

        return optimizedSchema;
    }

    optimizeVisualHierarchy(designSchema) {
        // Ensure proper visual hierarchy by adjusting weights and sizes
        const optimizedSchema = JSON.parse(JSON.stringify(designSchema));

        // Sort layers by importance
        optimizedSchema.layers.sort((a, b) => (b.weight || 0) - (a.weight || 0));

        // Adjust sizes based on hierarchy
        optimizedSchema.layers.forEach((layer, index) => {
            if (index === 0) {
                layer.size = 'large';
                layer.weight = 1.0;
            } else if (index === 1) {
                layer.size = 'medium';
                layer.weight = 0.7;
            } else {
                layer.size = 'small';
                layer.weight = 0.4;
            }
        });

        return optimizedSchema;
    }

    calculateVisualFlow(designSchema) {
        const flowPaths = [];

        // Create flow paths between focus points
        for (let i = 0; i < designSchema.focusPoints.length - 1; i++) {
            const start = designSchema.focusPoints[i];
            const end = designSchema.focusPoints[i + 1];

            flowPaths.push({
                start: { x: start.x, y: start.y },
                end: { x: end.x, y: end.y },
                strength: Math.min(start.intensity, end.intensity),
                type: 'focus_connection'
            });
        }

        // Add flow paths from composition layers
        designSchema.layers.forEach((layer, index) => {
            if (layer.position && index < designSchema.layers.length - 1) {
                const nextLayer = designSchema.layers[index + 1];
                if (nextLayer.position) {
                    flowPaths.push({
                        start: { x: layer.position.x, y: layer.position.y },
                        end: { x: nextLayer.position.x, y: nextLayer.position.y },
                        strength: (layer.weight + nextLayer.weight) / 2,
                        type: 'layer_connection'
                    });
                }
            }
        });

        return flowPaths;
    }

    calculateCenterOfMass(weightMap) {
        let totalWeight = 0;
        let weightedX = 0;
        let weightedY = 0;

        Object.keys(weightMap).forEach(x => {
            Object.keys(weightMap[x]).forEach(y => {
                const weight = weightMap[x][y];
                totalWeight += weight;
                weightedX += parseInt(x) * weight;
                weightedY += parseInt(y) * weight;
            });
        });

        return {
            x: totalWeight > 0 ? weightedX / totalWeight : this.canvas.width / 2,
            y: totalWeight > 0 ? weightedY / totalWeight : this.canvas.height / 2
        };
    }

    calculateCompositionBalance(designSchema) {
        // Calculate balance score (0-1, where 1 is perfectly balanced)
        const leftWeight = this.calculateRegionWeight(designSchema, 0, 0, this.canvas.width / 2, this.canvas.height);
        const rightWeight = this.calculateRegionWeight(designSchema, this.canvas.width / 2, 0, this.canvas.width / 2, this.canvas.height);
        const topWeight = this.calculateRegionWeight(designSchema, 0, 0, this.canvas.width, this.canvas.height / 2);
        const bottomWeight = this.calculateRegionWeight(designSchema, 0, this.canvas.height / 2, this.canvas.width, this.canvas.height / 2);

        const horizontalBalance = 1 - Math.abs(leftWeight - rightWeight) / Math.max(leftWeight, rightWeight, 0.1);
        const verticalBalance = 1 - Math.abs(topWeight - bottomWeight) / Math.max(topWeight, bottomWeight, 0.1);

        return (horizontalBalance + verticalBalance) / 2;
    }

    calculateRegionWeight(designSchema, x, y, width, height) {
        let weight = 0;

        designSchema.focusPoints.forEach(focusPoint => {
            if (focusPoint.x >= x && focusPoint.x <= x + width &&
                focusPoint.y >= y && focusPoint.y <= y + height) {
                weight += focusPoint.intensity;
            }
        });

        designSchema.layers.forEach(layer => {
            if (layer.position &&
                layer.position.x >= x && layer.position.x <= x + width &&
                layer.position.y >= y && layer.position.y <= y + height) {
                weight += layer.weight || 0.5;
            }
        });

        return weight;
    }

    calculateColorHarmony(colorPalette) {
        // Simple color harmony calculation based on color theory
        // This is a simplified version - in a real implementation, you'd use more sophisticated color analysis

        if (colorPalette.length < 2) return 1.0;

        let harmonyScore = 0;
        let comparisons = 0;

        for (let i = 0; i < colorPalette.length - 1; i++) {
            for (let j = i + 1; j < colorPalette.length; j++) {
                const harmony = this.calculateColorPairHarmony(colorPalette[i], colorPalette[j]);
                harmonyScore += harmony;
                comparisons++;
            }
        }

        return comparisons > 0 ? harmonyScore / comparisons : 1.0;
    }

    calculateColorPairHarmony(color1, color2) {
        // Convert hex to HSL and calculate harmony based on hue relationships
        const hsl1 = this.hexToHsl(color1);
        const hsl2 = this.hexToHsl(color2);

        const hueDiff = Math.abs(hsl1.h - hsl2.h);
        const normalizedHueDiff = Math.min(hueDiff, 360 - hueDiff);

        // Complementary colors (180°) and triadic colors (120°) score higher
        if (Math.abs(normalizedHueDiff - 180) < 30) return 1.0; // Complementary
        if (Math.abs(normalizedHueDiff - 120) < 20) return 0.9; // Triadic
        if (Math.abs(normalizedHueDiff - 60) < 15) return 0.8;  // Split complementary
        if (normalizedHueDiff < 30) return 0.7; // Analogous

        return 0.5; // Neutral harmony
    }

    hexToHsl(hex) {
        // Convert hex color to HSL
        const r = parseInt(hex.slice(1, 3), 16) / 255;
        const g = parseInt(hex.slice(3, 5), 16) / 255;
        const b = parseInt(hex.slice(5, 7), 16) / 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0; // achromatic
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return { h: h * 360, s: s * 100, l: l * 100 };
    }

    enhanceVisualFlow(flowPaths, colorPalette) {
        // Draw subtle connecting lines to enhance visual flow
        const originalAlpha = this.ctx.globalAlpha;
        this.ctx.globalAlpha = 0.2;

        flowPaths.forEach(path => {
            if (path.strength > 0.3) {
                this.ctx.strokeStyle = colorPalette[0];
                this.ctx.lineWidth = path.strength * 3;

                this.ctx.beginPath();
                this.ctx.moveTo(path.start.x, path.start.y);

                // Create curved connection
                const controlX = (path.start.x + path.end.x) / 2 + (Math.random() - 0.5) * 50;
                const controlY = (path.start.y + path.end.y) / 2 + (Math.random() - 0.5) * 50;

                this.ctx.quadraticCurveTo(controlX, controlY, path.end.x, path.end.y);
                this.ctx.stroke();
            }
        });

        this.ctx.globalAlpha = originalAlpha;
    }

    generateDesignMetadata(designSchema, composition) {
        return {
            complexity: designSchema.complexity,
            style: designSchema.style,
            theme: designSchema.theme,
            layerCount: designSchema.layers.length,
            focusPointCount: designSchema.focusPoints.length,
            colorCount: designSchema.colorPalette.length,
            balance: composition.balance,
            harmony: composition.harmony,
            canvasUtilization: this.calculateCanvasUtilization(designSchema),
            estimatedTime: this.estimateCreationTime(designSchema),
            difficulty: this.calculateDifficulty(designSchema, composition)
        };
    }

    calculateCanvasUtilization(designSchema) {
        // Calculate what percentage of the canvas is actively used
        let utilizedArea = 0;
        const totalArea = this.canvas.width * this.canvas.height;

        // Add area from focus points
        designSchema.focusPoints.forEach(focusPoint => {
            utilizedArea += Math.PI * focusPoint.radius * focusPoint.radius;
        });

        // Add area from composition layers (estimated)
        designSchema.layers.forEach(layer => {
            if (layer.size === 'large') utilizedArea += 15000;
            else if (layer.size === 'medium') utilizedArea += 8000;
            else utilizedArea += 3000;
        });

        // Add area from texture areas
        designSchema.textureAreas.forEach(textureArea => {
            utilizedArea += textureArea.width * textureArea.height;
        });

        return Math.min(1, utilizedArea / totalArea);
    }

    estimateCreationTime(designSchema) {
        // Estimate time in hours to create physically
        let baseTime = 2; // Base time for setup and finishing

        // Add time for each layer
        baseTime += designSchema.layers.length * 0.5;

        // Add time for focus points
        baseTime += designSchema.focusPoints.length * 1;

        // Add time for texture areas
        baseTime += designSchema.textureAreas.length * 0.3;

        // Multiply by complexity factor
        const complexityMultiplier = {
            'low': 1,
            'medium': 1.5,
            'high': 2,
            'very_high': 3
        };

        return baseTime * (complexityMultiplier[designSchema.complexity] || 1);
    }

    calculateDifficulty(designSchema, composition) {
        let difficultyScore = 0;

        // Layer complexity
        difficultyScore += designSchema.layers.length * 0.1;

        // Focus point complexity
        difficultyScore += designSchema.focusPoints.length * 0.15;

        // Color complexity
        difficultyScore += designSchema.colorPalette.length * 0.05;

        // Balance difficulty (harder to achieve good balance)
        difficultyScore += (1 - composition.balance) * 0.3;

        // Style complexity
        const styleComplexity = {
            'minimalist': 0.1,
            'organic': 0.3,
            'geometric': 0.5,
            'artDeco': 0.7,
            'bohemian': 0.9
        };

        difficultyScore += styleComplexity[designSchema.style] || 0.5;

        if (difficultyScore < 0.3) return 'beginner';
        if (difficultyScore < 0.6) return 'intermediate';
        if (difficultyScore < 0.8) return 'advanced';
        return 'expert';
    }
}
